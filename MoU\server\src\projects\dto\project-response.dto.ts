import { ApiProperty } from '@nestjs/swagger';

export class ProjectResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the project',
    example: 'uuid-project-id'
  })
  id: string;

  @ApiProperty({
    description: 'Project identifier',
    example: 'PROJ-2024-001'
  })
  projectId: string;

  @ApiProperty({
    description: 'Project name',
    example: 'Rural Healthcare Initiative'
  })
  name: string;

  @ApiProperty({
    description: 'Project description',
    required: false
  })
  description?: string;

  @ApiProperty({
    description: 'Project duration in months',
    example: 36
  })
  duration: number;

  @ApiProperty({
    description: 'Project start date',
    required: false
  })
  startDate?: string;

  @ApiProperty({
    description: 'Project end date',
    required: false
  })
  endDate?: string;

  @ApiProperty({
    description: 'Total project budget',
    required: false
  })
  totalBudget?: number;

  @ApiProperty({
    description: 'Currency code',
    required: false
  })
  currency?: string;

  @ApiProperty({
    description: 'Budget type ID',
    example: 1
  })
  budgetTypeId: number;

  @ApiProperty({
    description: 'Funding unit ID',
    example: 1
  })
  fundingUnitId: number;

  @ApiProperty({
    description: 'Funding source ID',
    example: 1
  })
  fundingSourceId: number;

  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-organization-id'
  })
  organizationId: string;

  @ApiProperty({
    description: 'Project document ID',
    example: 'uuid-document-id'
  })
  projectDocumentId: string;

  @ApiProperty({
    description: 'MoU application ID',
    example: 'uuid-application-id'
  })
  mouApplicationId: string;

  @ApiProperty({
    description: 'Creation timestamp'
  })
  createAt: string;

  @ApiProperty({
    description: 'Last update timestamp'
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Soft delete flag'
  })
  deleted: boolean;

  @ApiProperty({
    description: 'Associated budget type information',
    required: false
  })
  budgetType?: {
    id: number;
    typeName: string;
  };

  @ApiProperty({
    description: 'Associated funding unit information',
    required: false
  })
  fundingUnit?: {
    id: number;
    unitName: string;
  };

  @ApiProperty({
    description: 'Associated funding source information',
    required: false
  })
  fundingSource?: {
    id: number;
    sourceName: string;
  };

  @ApiProperty({
    description: 'Associated organization information',
    required: false
  })
  organization?: {
    id: string;
    organizationName: string;
  };

  @ApiProperty({
    description: 'Project activities',
    required: false,
    isArray: true
  })
  projectActivities?: any[];

  @ApiProperty({
    description: 'Activities (legacy)',
    required: false,
    isArray: true
  })
  activities?: any[];
}
