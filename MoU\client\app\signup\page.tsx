"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { MultiStepRegistration } from "@/components/multi-step-registration"
import { HeartPulse } from "lucide-react"
import Image from "next/image"

export default function SignupPage() {
  const [showRegistration, setShowRegistration] = useState(false)
  const [success, setSuccess] = useState(false)
  const router = useRouter()

  const handleStartRegistration = () => {
    setShowRegistration(true)
  }

  const handleRegistrationSuccess = () => {
    setSuccess(true)
    setShowRegistration(false)
    // Redirect to verification page after 3 seconds
    setTimeout(() => {
      router.push("/auth/verify-email?message=Please check your email to verify your account")
    }, 3000)
  }

  const handleCancel = () => {
    setShowRegistration(false)
  }

  if (showRegistration) {
    return (
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <MultiStepRegistration
          onSuccess={handleRegistrationSuccess}
          onCancel={handleCancel}
        />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      {success ? (
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <HeartPulse className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-2xl font-bold text-green-900">Registration Successful!</CardTitle>
            <CardDescription>
              Your partner registration has been submitted successfully.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="bg-green-50 border-green-200">
              <AlertDescription className="text-green-800">
                Please check your email for verification instructions. You will be redirected to the login page shortly.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Link href="/login" className="w-full">
              <Button className="w-full">Go to Login</Button>
            </Link>
          </CardFooter>
        </Card>
      ) : (
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-cyan-100">
              <Image src={'/logo.png'} alt="MoU Management System" width={48} height={48} />
            </div>
            <CardTitle className="text-2xl font-bold">Partner Registration</CardTitle>
            <CardDescription>
              Register your organization as a partner with the Ministry of Health
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-gray-600 space-y-2">
              <p>To register as a partner, you will need to provide:</p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Your personal account information</li>
                <li>Organization details and registration documents</li>
                <li>Organization address information</li>
              </ul>
            </div>
            <Button onClick={handleStartRegistration} className="w-full bg-cyan-600 hover:bg-cyan-700">
              Start Registration Process
            </Button>
          </CardContent>
          <CardFooter>
            <p className="text-center text-sm text-muted-foreground w-full">
              Already have an account?{" "}
              <Link href="/login" className="text-cyan-600 hover:underline">
                Login
              </Link>
            </p>
          </CardFooter>
        </Card>
      )}
    </div>
  )
}
