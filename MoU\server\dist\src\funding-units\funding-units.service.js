"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FundingUnitsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FundingUnitsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let FundingUnitsService = FundingUnitsService_1 = class FundingUnitsService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(FundingUnitsService_1.name);
    }
    async findAll() {
        try {
            const fundingUnits = await this.prisma.fundingUnit.findMany({
                where: { deleted: false },
                orderBy: { createAt: 'desc' }
            });
            return fundingUnits;
        }
        catch (error) {
            this.logger.error('Failed to fetch funding units', error.stack);
            throw new Error('Failed to fetch funding units');
        }
    }
    async findOne(id) {
        try {
            const fundingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });
            if (!fundingUnit) {
                throw new common_1.NotFoundException('Funding unit not found');
            }
            return fundingUnit;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to fetch funding unit');
        }
    }
    async create(createFundingUnitDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create funding units');
            }
            const existingUnit = await this.prisma.fundingUnit.findFirst({
                where: {
                    unitName: createFundingUnitDto.unitName,
                    deleted: false
                }
            });
            if (existingUnit) {
                throw new common_1.ConflictException('Funding unit with this name already exists');
            }
            const fundingUnit = await this.prisma.fundingUnit.create({
                data: {
                    unitName: createFundingUnitDto.unitName
                }
            });
            return fundingUnit;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create funding unit', error.stack);
            throw new Error('Failed to create funding unit');
        }
    }
    async update(id, updateFundingUnitDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update funding units');
            }
            const existingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });
            if (!existingUnit) {
                throw new common_1.NotFoundException('Funding unit not found');
            }
            if (updateFundingUnitDto.unitName && updateFundingUnitDto.unitName !== existingUnit.unitName) {
                const nameConflict = await this.prisma.fundingUnit.findFirst({
                    where: {
                        unitName: updateFundingUnitDto.unitName,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException('Funding unit with this name already exists');
                }
            }
            const fundingUnit = await this.prisma.fundingUnit.update({
                where: { id },
                data: updateFundingUnitDto
            });
            return fundingUnit;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to update funding unit');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete funding units');
            }
            const existingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });
            if (!existingUnit) {
                throw new common_1.NotFoundException('Funding unit not found');
            }
            const projectsUsingUnit = await this.prisma.project.findFirst({
                where: {
                    fundingUnitId: id,
                    deleted: false
                }
            });
            if (projectsUsingUnit) {
                throw new common_1.ConflictException('Cannot delete funding unit that is being used by projects');
            }
            await this.prisma.fundingUnit.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'Funding unit deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to delete funding unit');
        }
    }
};
exports.FundingUnitsService = FundingUnitsService;
exports.FundingUnitsService = FundingUnitsService = FundingUnitsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], FundingUnitsService);
//# sourceMappingURL=funding-units.service.js.map