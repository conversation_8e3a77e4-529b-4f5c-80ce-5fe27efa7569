import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCurrencyDto, UpdateCurrencyDto } from './dto';

@Injectable()
export class CurrenciesService {
    private readonly logger = new Logger(CurrenciesService.name);

    constructor(private prisma: PrismaService) {}

    async findAll() {
        try {
            const currencies = await this.prisma.currency.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });

            return currencies;
        } catch (error) {
            this.logger.error('Failed to fetch currencies', error.stack);
            throw new Error('Failed to fetch currencies');
        }
    }

    async findOne(id: number) {
        try {
            const currency = await this.prisma.currency.findFirst({
                where: { 
                    id,
                    deleted: false 
                }
            });

            if (!currency) {
                throw new NotFoundException('Currency not found');
            }

            return currency;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error('Failed to fetch currency', error.stack);
            throw new Error('Failed to fetch currency');
        }
    }

    async create(createCurrencyDto: CreateCurrencyDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create currencies
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create currencies');
            }

            // Check if currency with same code already exists
            const existingCurrency = await this.prisma.currency.findFirst({
                where: { 
                    currencyCode: createCurrencyDto.currencyCode.toUpperCase(),
                    deleted: false 
                }
            });

            if (existingCurrency) {
                throw new ConflictException('Currency with this code already exists');
            }

            const currency = await this.prisma.currency.create({
                data: {
                    currencyCode: createCurrencyDto.currencyCode.toUpperCase(),
                    currencyName: createCurrencyDto.currencyName
                }
            });

            return currency;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create currency', error.stack);
            throw new Error('Failed to create currency');
        }
    }

    async update(id: number, updateCurrencyDto: UpdateCurrencyDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can update currencies
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update currencies');
            }

            // Check if currency exists
            const existingCurrency = await this.prisma.currency.findFirst({
                where: { 
                    id,
                    deleted: false 
                }
            });

            if (!existingCurrency) {
                throw new NotFoundException('Currency not found');
            }

            // Check if new currency code conflicts with existing currencies
            if (updateCurrencyDto.currencyCode && updateCurrencyDto.currencyCode.toUpperCase() !== existingCurrency.currencyCode) {
                const codeConflict = await this.prisma.currency.findFirst({
                    where: { 
                        currencyCode: updateCurrencyDto.currencyCode.toUpperCase(),
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (codeConflict) {
                    throw new ConflictException('Currency with this code already exists');
                }
            }

            const updateData: any = {};
            if (updateCurrencyDto.currencyCode) {
                updateData.currencyCode = updateCurrencyDto.currencyCode.toUpperCase();
            }
            if (updateCurrencyDto.currencyName) {
                updateData.currencyName = updateCurrencyDto.currencyName;
            }

            const currency = await this.prisma.currency.update({
                where: { id },
                data: updateData
            });

            return currency;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update currency', error.stack);
            throw new Error('Failed to update currency');
        }
    }

    async remove(id: number, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete currencies
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete currencies');
            }

            // Check if currency exists
            const existingCurrency = await this.prisma.currency.findFirst({
                where: { 
                    id,
                    deleted: false 
                }
            });

            if (!existingCurrency) {
                throw new NotFoundException('Currency not found');
            }

            // TODO: Check if currency is being used in any projects or other entities
            // For now, we'll allow deletion but this should be implemented based on business rules

            // Soft delete the currency
            await this.prisma.currency.update({
                where: { id },
                data: { deleted: true }
            });

            return { message: 'Currency deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete currency', error.stack);
            throw new Error('Failed to delete currency');
        }
    }
}
