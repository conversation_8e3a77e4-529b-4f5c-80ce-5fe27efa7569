"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
const uuid_1 = require("uuid");
const fs = require("fs");
let MouApplicationsService = class MouApplicationsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createMouApplicationDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            include: { organization: true }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const existingApplication = await this.prisma.mouApplication.findUnique({
            where: { mouApplicationId: createMouApplicationDto.mouApplicationId }
        });
        if (existingApplication) {
            throw new common_1.ConflictException('Application with this ID already exists');
        }
        const mou = await this.prisma.mou.findUnique({
            where: { id: createMouApplicationDto.mouId }
        });
        if (!mou) {
            throw new common_1.NotFoundException('MoU not found');
        }
        return this.prisma.mouApplication.create({
            data: {
                ...createMouApplicationDto,
                userId,
                status: createMouApplicationDto.status || client_1.ApplicationStatus.DRAFT,
                currentStep: createMouApplicationDto.currentStep || 1,
                completionPercentage: createMouApplicationDto.completionPercentage || 0,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: true,
                responsibilities: true,
                documents: true,
                approvalSteps: true,
            }
        });
    }
    async createDraft(mouId, userId) {
        const applicationId = `APP-${new Date().getFullYear()}-${Date.now()}`;
        return this.create({
            mouApplicationId: applicationId,
            mouId,
            status: client_1.ApplicationStatus.DRAFT,
            currentStep: 1,
            completionPercentage: 0,
        }, userId);
    }
    async findAll(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const whereClause = user.role === 'ADMIN'
            ? { deleted: false }
            : { deleted: false, userId };
        return this.prisma.mouApplication.findMany({
            where: whereClause,
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: true,
                responsibilities: {
                    orderBy: { order: 'asc' }
                },
                documents: true,
                approvalSteps: true,
            },
            orderBy: { createdAt: 'desc' }
        });
    }
    async findMyApplications(userId) {
        return this.prisma.mouApplication.findMany({
            where: {
                deleted: false,
                userId
            },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: true,
                responsibilities: {
                    orderBy: { order: 'asc' }
                },
                documents: true,
                approvalSteps: true,
            },
            orderBy: { createdAt: 'desc' }
        });
    }
    async findOne(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const application = await this.prisma.mouApplication.findUnique({
            where: { id, deleted: false },
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: {
                    include: {
                        projectActivities: true
                    }
                },
                responsibilities: {
                    orderBy: { order: 'asc' }
                },
                documents: true,
                approvalSteps: {
                    include: {
                        reviewer: {
                            select: {
                                id: true,
                                firstName: true,
                                lastName: true,
                                email: true,
                                role: true,
                            }
                        }
                    }
                },
            }
        });
        if (!application) {
            throw new common_1.NotFoundException('Application not found');
        }
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return application;
    }
    async update(id, updateMouApplicationDto, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (updateMouApplicationDto.mouApplicationId &&
            updateMouApplicationDto.mouApplicationId !== application.mouApplicationId) {
            const existingApplication = await this.prisma.mouApplication.findUnique({
                where: { mouApplicationId: updateMouApplicationDto.mouApplicationId }
            });
            if (existingApplication) {
                throw new common_1.ConflictException('Application with this ID already exists');
            }
        }
        return this.prisma.mouApplication.update({
            where: { id },
            data: updateMouApplicationDto,
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: true,
                responsibilities: {
                    orderBy: { order: 'asc' }
                },
                documents: true,
                approvalSteps: true,
            }
        });
    }
    async updateStep(id, stepNumber, updateStepDto, userId) {
        const application = await this.findOne(id, userId);
        if (stepNumber < 1 || stepNumber > 5) {
            throw new common_1.BadRequestException('Step number must be between 1 and 5');
        }
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const updateData = {
            currentStep: stepNumber,
            lastAutoSave: new Date(),
        };
        if (updateStepDto.responsibility !== undefined)
            updateData.responsibility = updateStepDto.responsibility;
        if (updateStepDto.mouDurationYears !== undefined)
            updateData.mouDurationYears = updateStepDto.mouDurationYears;
        if (updateStepDto.signatoryName !== undefined)
            updateData.signatoryName = updateStepDto.signatoryName;
        if (updateStepDto.signatoryPosition !== undefined)
            updateData.signatoryPosition = updateStepDto.signatoryPosition;
        if (updateStepDto.partyName !== undefined)
            updateData.partyName = updateStepDto.partyName;
        if (updateStepDto.completionPercentage !== undefined)
            updateData.completionPercentage = updateStepDto.completionPercentage;
        if (updateStepDto.status !== undefined)
            updateData.status = updateStepDto.status;
        return this.prisma.mouApplication.update({
            where: { id },
            data: updateData,
            include: {
                user: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        organization: {
                            select: {
                                id: true,
                                organizationName: true,
                            }
                        }
                    }
                },
                mou: {
                    select: {
                        id: true,
                        mouId: true,
                        party: {
                            select: {
                                id: true,
                                name: true,
                            }
                        }
                    }
                },
                projects: true,
                responsibilities: {
                    orderBy: { order: 'asc' }
                },
                documents: true,
                approvalSteps: true,
            }
        });
    }
    async autoSave(id, autoSaveDto, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const updateData = {
            lastAutoSave: new Date(),
        };
        if (autoSaveDto.currentStep !== undefined)
            updateData.currentStep = autoSaveDto.currentStep;
        if (autoSaveDto.completionPercentage !== undefined)
            updateData.completionPercentage = autoSaveDto.completionPercentage;
        if (autoSaveDto.responsibility !== undefined)
            updateData.responsibility = autoSaveDto.responsibility;
        if (autoSaveDto.mouDurationYears !== undefined)
            updateData.mouDurationYears = autoSaveDto.mouDurationYears;
        if (autoSaveDto.signatoryName !== undefined)
            updateData.signatoryName = autoSaveDto.signatoryName;
        if (autoSaveDto.signatoryPosition !== undefined)
            updateData.signatoryPosition = autoSaveDto.signatoryPosition;
        if (autoSaveDto.partyName !== undefined)
            updateData.partyName = autoSaveDto.partyName;
        return this.prisma.mouApplication.update({
            where: { id },
            data: updateData,
            select: {
                id: true,
                lastAutoSave: true,
                currentStep: true,
                completionPercentage: true,
            }
        });
    }
    async createResponsibility(id, createResponsibilityDto, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return this.prisma.applicationResponsibility.create({
            data: {
                ...createResponsibilityDto,
                mouApplicationId: id,
            }
        });
    }
    async updateResponsibility(id, responsibilityId, updateData, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const responsibility = await this.prisma.applicationResponsibility.findFirst({
            where: {
                id: responsibilityId,
                mouApplicationId: id,
                deleted: false
            }
        });
        if (!responsibility) {
            throw new common_1.NotFoundException('Responsibility not found');
        }
        return this.prisma.applicationResponsibility.update({
            where: { id: responsibilityId },
            data: updateData
        });
    }
    async deleteResponsibility(id, responsibilityId, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const responsibility = await this.prisma.applicationResponsibility.findFirst({
            where: {
                id: responsibilityId,
                mouApplicationId: id,
                deleted: false
            }
        });
        if (!responsibility) {
            throw new common_1.NotFoundException('Responsibility not found');
        }
        await this.prisma.applicationResponsibility.update({
            where: { id: responsibilityId },
            data: { deleted: true }
        });
        return { message: 'Responsibility deleted successfully' };
    }
    async uploadDocument(id, file, uploadDocumentDto, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        const documentId = `DOC-${Date.now()}-${(0, uuid_1.v4)()}`;
        return this.prisma.applicationDocument.create({
            data: {
                documentId,
                fileName: file.filename,
                originalName: file.originalname,
                mimeType: file.mimetype,
                fileSize: file.size,
                filePath: file.path,
                documentType: uploadDocumentDto.documentType,
                isRequired: uploadDocumentDto.isRequired || false,
                mouApplicationId: id,
            }
        });
    }
    async deleteDocument(id, documentId, userId) {
        const application = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const document = await this.prisma.applicationDocument.findFirst({
            where: {
                id: documentId,
                mouApplicationId: id,
                deleted: false
            }
        });
        if (!document) {
            throw new common_1.NotFoundException('Document not found');
        }
        try {
            if (fs.existsSync(document.filePath)) {
                fs.unlinkSync(document.filePath);
            }
        }
        catch (error) {
            console.error('Error deleting file:', error);
        }
        await this.prisma.applicationDocument.update({
            where: { id: documentId },
            data: { deleted: true }
        });
        return { message: 'Document deleted successfully' };
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const application = await this.findOne(id, userId);
        if (user.role !== 'ADMIN' && application.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (application.status !== client_1.ApplicationStatus.DRAFT) {
            throw new common_1.BadRequestException('Only draft applications can be deleted');
        }
        const documents = await this.prisma.applicationDocument.findMany({
            where: { mouApplicationId: id, deleted: false }
        });
        for (const document of documents) {
            try {
                if (fs.existsSync(document.filePath)) {
                    fs.unlinkSync(document.filePath);
                }
            }
            catch (error) {
                console.error('Error deleting file:', error);
            }
        }
        await this.prisma.mouApplication.update({
            where: { id },
            data: { deleted: true }
        });
        return { message: 'Application deleted successfully' };
    }
};
exports.MouApplicationsService = MouApplicationsService;
exports.MouApplicationsService = MouApplicationsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MouApplicationsService);
//# sourceMappingURL=mou-applications.service.js.map