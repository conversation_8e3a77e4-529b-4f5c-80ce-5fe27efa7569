import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsInt, Min } from 'class-validator';

export class CreateResponsibilityDto {
  @ApiProperty({
    description: 'Responsibility text/description',
    example: 'Provide healthcare services to rural communities'
  })
  @IsString()
  @IsNotEmpty()
  responsibilityText: string;

  @ApiProperty({
    description: 'Order/sequence of this responsibility',
    minimum: 1,
    example: 1
  })
  @IsInt()
  @Min(1)
  order: number;
}
