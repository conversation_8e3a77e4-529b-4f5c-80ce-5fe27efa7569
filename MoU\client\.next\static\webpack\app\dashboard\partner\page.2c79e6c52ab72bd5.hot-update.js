"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/page",{

/***/ "(app-pages-browser)/./lib/services/mou-application.service.ts":
/*!*************************************************!*\
  !*** ./lib/services/mou-application.service.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mouApplicationService: () => (/* binding */ mouApplicationService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./lib/api.ts\");\n\nconst mouApplicationService = {\n    // Get all MoU applications for the current user (partner)\n    async getMyApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/my-applications\");\n        return response.data;\n    },\n    // Get all MoU applications (admin/reviewer access)\n    async getAllApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications\");\n        return response.data;\n    },\n    // Get a specific MoU application by ID\n    async getApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Create a new MoU application (draft by default)\n    async createApplication (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications\", {\n            ...data,\n            status: data.status || 'DRAFT',\n            currentStep: data.currentStep || 1\n        });\n        return response.data;\n    },\n    // Create a draft application with minimal data\n    async createDraft (mouId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/draft\", {\n            mouId,\n            status: 'DRAFT',\n            currentStep: 1,\n            completionPercentage: 0\n        });\n        return response.data;\n    },\n    // Update an existing MoU application\n    async updateApplication (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/mou-applications/\".concat(id), data);\n        return response.data;\n    },\n    // Update a specific step of the application\n    async updateStep (id, stepNumber, data) {\n        let autoSave = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/mou-applications/\".concat(id, \"/step/\").concat(stepNumber), {\n            data,\n            autoSave,\n            lastAutoSave: autoSave ? new Date().toISOString() : undefined\n        });\n        return response.data;\n    },\n    // Auto-save application data\n    async autoSave (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(id, \"/auto-save\"), {\n            ...data,\n            lastAutoSave: new Date().toISOString()\n        });\n        return response.data;\n    },\n    // Delete a MoU application\n    async deleteApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Submit application for review (convert from DRAFT to SUBMITTED)\n    async submitApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(id, \"/submit\"));\n        return response.data;\n    },\n    // Get available MoUs for application\n    async getAvailableMous () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mous/available\");\n        return response.data;\n    },\n    // Get application statistics for dashboard\n    async getApplicationStats () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/stats\");\n        return response.data;\n    },\n    // Document upload methods\n    async uploadDocument (applicationId, file, documentType) {\n        let isRequired = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('documentType', documentType);\n        formData.append('isRequired', isRequired.toString());\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/documents\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    async deleteDocument (applicationId, documentId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(applicationId, \"/documents/\").concat(documentId));\n        return response.data;\n    },\n    // Responsibility management\n    async addResponsibility (applicationId, responsibilityText, order) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/responsibilities\"), {\n            responsibilityText,\n            order\n        });\n        return response.data;\n    },\n    async updateResponsibility (applicationId, responsibilityId, responsibilityText) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/mou-applications/\".concat(applicationId, \"/responsibilities/\").concat(responsibilityId), {\n            responsibilityText\n        });\n        return response.data;\n    },\n    async deleteResponsibility (applicationId, responsibilityId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(applicationId, \"/responsibilities/\").concat(responsibilityId));\n        return response.data;\n    },\n    // Project activity management\n    async addProjectActivity (projectId, activity) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/projects/\".concat(projectId, \"/activities\"), activity);\n        return response.data;\n    },\n    async updateProjectActivity (projectId, activityId, activity) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/projects/\".concat(projectId, \"/activities/\").concat(activityId), activity);\n        return response.data;\n    },\n    async deleteProjectActivity (projectId, activityId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/projects/\".concat(projectId, \"/activities/\").concat(activityId));\n        return response.data;\n    },\n    // Validation methods\n    async validateStep (applicationId, stepNumber) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/validate-step/\").concat(stepNumber));\n        return response.data;\n    },\n    // Progress calculation\n    calculateCompletionPercentage (application) {\n        let completedSteps = 0;\n        const totalSteps = 5;\n        // Step 1: MoU Information\n        if (application.mouDurationYears) {\n            completedSteps++;\n        }\n        // Step 2: Party Details\n        if (application.partyName && application.signatoryName && application.signatoryPosition && application.responsibilities && application.responsibilities.length > 0) {\n            completedSteps++;\n        }\n        // Step 3: Project Information\n        if (application.projects && application.projects.length > 0) {\n            const hasValidProjects = application.projects.some((p)=>p.projectName && p.startDate && p.endDate && p.totalBudget);\n            if (hasValidProjects) {\n                completedSteps++;\n            }\n        }\n        // Step 4: Document Upload\n        if (application.documents && application.documents.length > 0) {\n            const hasRequiredDocs = application.documents.some((d)=>d.isRequired);\n            if (hasRequiredDocs) {\n                completedSteps++;\n            }\n        }\n        // Step 5: Review & Submit (completed when status is not DRAFT)\n        if (application.status !== 'DRAFT') {\n            completedSteps++;\n        }\n        return Math.round(completedSteps / totalSteps * 100);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9zZXJ2aWNlcy9tb3UtYXBwbGljYXRpb24uc2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QjtBQTBQakIsTUFBTUMsd0JBQXdCO0lBQ25DLDBEQUEwRDtJQUMxRCxNQUFNQztRQUNKLE1BQU1DLFdBQVcsTUFBTUgsNENBQUdBLENBQUNJLEdBQUcsQ0FBQztRQUMvQixPQUFPRCxTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsbURBQW1EO0lBQ25ELE1BQU1DO1FBQ0osTUFBTUgsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ0ksR0FBRyxDQUFDO1FBQy9CLE9BQU9ELFNBQVNFLElBQUk7SUFDdEI7SUFFQSx1Q0FBdUM7SUFDdkMsTUFBTUUsZ0JBQWVDLEVBQVU7UUFDN0IsTUFBTUwsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ0ksR0FBRyxDQUFDLHFCQUF3QixPQUFISTtRQUNwRCxPQUFPTCxTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU1JLG1CQUFrQkosSUFBaUM7UUFDdkQsTUFBTUYsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ1UsSUFBSSxDQUFDLHFCQUFxQjtZQUNuRCxHQUFHTCxJQUFJO1lBQ1BNLFFBQVFOLEtBQUtNLE1BQU0sSUFBSTtZQUN2QkMsYUFBYVAsS0FBS08sV0FBVyxJQUFJO1FBQ25DO1FBQ0EsT0FBT1QsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLCtDQUErQztJQUMvQyxNQUFNUSxhQUFZQyxLQUFhO1FBQzdCLE1BQU1YLFdBQVcsTUFBTUgsNENBQUdBLENBQUNVLElBQUksQ0FBQywyQkFBMkI7WUFDekRJO1lBQ0FILFFBQVE7WUFDUkMsYUFBYTtZQUNiRyxzQkFBc0I7UUFDeEI7UUFDQSxPQUFPWixTQUFTRSxJQUFJO0lBQ3RCO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU1XLG1CQUFrQlIsRUFBVSxFQUFFSCxJQUFpQztRQUNuRSxNQUFNRixXQUFXLE1BQU1ILDRDQUFHQSxDQUFDaUIsR0FBRyxDQUFDLHFCQUF3QixPQUFIVCxLQUFNSDtRQUMxRCxPQUFPRixTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1hLFlBQVdWLEVBQVUsRUFBRVcsVUFBa0IsRUFBRWQsSUFBUztZQUFFZSxXQUFBQSxpRUFBVztRQUNyRSxNQUFNakIsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ3FCLEtBQUssQ0FBQyxxQkFBZ0NGLE9BQVhYLElBQUcsVUFBbUIsT0FBWFcsYUFBYztZQUM3RWQ7WUFDQWU7WUFDQUUsY0FBY0YsV0FBVyxJQUFJRyxPQUFPQyxXQUFXLEtBQUtDO1FBQ3REO1FBQ0EsT0FBT3RCLFNBQVNFLElBQUk7SUFDdEI7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTWUsVUFBU1osRUFBVSxFQUFFSCxJQUFTO1FBQ2xDLE1BQU1GLFdBQVcsTUFBTUgsNENBQUdBLENBQUNVLElBQUksQ0FBQyxxQkFBd0IsT0FBSEYsSUFBRyxlQUFhO1lBQ25FLEdBQUdILElBQUk7WUFDUGlCLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztRQUN0QztRQUNBLE9BQU9yQixTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1xQixtQkFBa0JsQixFQUFVO1FBQ2hDLE1BQU1MLFdBQVcsTUFBTUgsNENBQUdBLENBQUMyQixNQUFNLENBQUMscUJBQXdCLE9BQUhuQjtRQUN2RCxPQUFPTCxTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU11QixtQkFBa0JwQixFQUFVO1FBQ2hDLE1BQU1MLFdBQVcsTUFBTUgsNENBQUdBLENBQUNVLElBQUksQ0FBQyxxQkFBd0IsT0FBSEYsSUFBRztRQUN4RCxPQUFPTCxTQUFTRSxJQUFJO0lBQ3RCO0lBRUEscUNBQXFDO0lBQ3JDLE1BQU13QjtRQUNKLE1BQU0xQixXQUFXLE1BQU1ILDRDQUFHQSxDQUFDSSxHQUFHLENBQUM7UUFDL0IsT0FBT0QsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLDJDQUEyQztJQUMzQyxNQUFNeUI7UUFPSixNQUFNM0IsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ0ksR0FBRyxDQUFDO1FBQy9CLE9BQU9ELFNBQVNFLElBQUk7SUFDdEI7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTTBCLGdCQUFlQyxhQUFxQixFQUFFQyxJQUFVLEVBQUVDLFlBQW9CO1lBQUVDLGFBQUFBLGlFQUFhO1FBQ3pGLE1BQU1DLFdBQVcsSUFBSUM7UUFDckJELFNBQVNFLE1BQU0sQ0FBQyxRQUFRTDtRQUN4QkcsU0FBU0UsTUFBTSxDQUFDLGdCQUFnQko7UUFDaENFLFNBQVNFLE1BQU0sQ0FBQyxjQUFjSCxXQUFXSSxRQUFRO1FBRWpELE1BQU1wQyxXQUFXLE1BQU1ILDRDQUFHQSxDQUFDVSxJQUFJLENBQUMscUJBQW1DLE9BQWRzQixlQUFjLGVBQWFJLFVBQVU7WUFDeEZJLFNBQVM7Z0JBQ1AsZ0JBQWdCO1lBQ2xCO1FBQ0Y7UUFDQSxPQUFPckMsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLE1BQU1vQyxnQkFBZVQsYUFBcUIsRUFBRVUsVUFBa0I7UUFDNUQsTUFBTXZDLFdBQVcsTUFBTUgsNENBQUdBLENBQUMyQixNQUFNLENBQUMscUJBQWdEZSxPQUEzQlYsZUFBYyxlQUF3QixPQUFYVTtRQUNsRixPQUFPdkMsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNc0MsbUJBQWtCWCxhQUFxQixFQUFFWSxrQkFBMEIsRUFBRUMsS0FBYTtRQUN0RixNQUFNMUMsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ1UsSUFBSSxDQUFDLHFCQUFtQyxPQUFkc0IsZUFBYyxzQkFBb0I7WUFDckZZO1lBQ0FDO1FBQ0Y7UUFDQSxPQUFPMUMsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLE1BQU15QyxzQkFBcUJkLGFBQXFCLEVBQUVlLGdCQUF3QixFQUFFSCxrQkFBMEI7UUFDcEcsTUFBTXpDLFdBQVcsTUFBTUgsNENBQUdBLENBQUNpQixHQUFHLENBQUMscUJBQXVEOEIsT0FBbENmLGVBQWMsc0JBQXFDLE9BQWpCZSxtQkFBb0I7WUFDeEdIO1FBQ0Y7UUFDQSxPQUFPekMsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLE1BQU0yQyxzQkFBcUJoQixhQUFxQixFQUFFZSxnQkFBd0I7UUFDeEUsTUFBTTVDLFdBQVcsTUFBTUgsNENBQUdBLENBQUMyQixNQUFNLENBQUMscUJBQXVEb0IsT0FBbENmLGVBQWMsc0JBQXFDLE9BQWpCZTtRQUN6RixPQUFPNUMsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLDhCQUE4QjtJQUM5QixNQUFNNEMsb0JBQW1CQyxTQUFpQixFQUFFQyxRQUErQjtRQUN6RSxNQUFNaEQsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ1UsSUFBSSxDQUFDLGFBQXVCLE9BQVZ3QyxXQUFVLGdCQUFjQztRQUNyRSxPQUFPaEQsU0FBU0UsSUFBSTtJQUN0QjtJQUVBLE1BQU0rQyx1QkFBc0JGLFNBQWlCLEVBQUVHLFVBQWtCLEVBQUVGLFFBQStCO1FBQ2hHLE1BQU1oRCxXQUFXLE1BQU1ILDRDQUFHQSxDQUFDaUIsR0FBRyxDQUFDLGFBQXFDb0MsT0FBeEJILFdBQVUsZ0JBQXlCLE9BQVhHLGFBQWNGO1FBQ2xGLE9BQU9oRCxTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsTUFBTWlELHVCQUFzQkosU0FBaUIsRUFBRUcsVUFBa0I7UUFDL0QsTUFBTWxELFdBQVcsTUFBTUgsNENBQUdBLENBQUMyQixNQUFNLENBQUMsYUFBcUMwQixPQUF4QkgsV0FBVSxnQkFBeUIsT0FBWEc7UUFDdkUsT0FBT2xELFNBQVNFLElBQUk7SUFDdEI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTWtELGNBQWF2QixhQUFxQixFQUFFYixVQUFrQjtRQUMxRCxNQUFNaEIsV0FBVyxNQUFNSCw0Q0FBR0EsQ0FBQ1UsSUFBSSxDQUFDLHFCQUFvRFMsT0FBL0JhLGVBQWMsbUJBQTRCLE9BQVhiO1FBQ3BGLE9BQU9oQixTQUFTRSxJQUFJO0lBQ3RCO0lBRUEsdUJBQXVCO0lBQ3ZCbUQsK0JBQThCQyxXQUEyQjtRQUN2RCxJQUFJQyxpQkFBaUI7UUFDckIsTUFBTUMsYUFBYTtRQUVuQiwwQkFBMEI7UUFDMUIsSUFBSUYsWUFBWUcsZ0JBQWdCLEVBQUU7WUFDaENGO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsSUFBSUQsWUFBWUksU0FBUyxJQUFJSixZQUFZSyxhQUFhLElBQUlMLFlBQVlNLGlCQUFpQixJQUNuRk4sWUFBWU8sZ0JBQWdCLElBQUlQLFlBQVlPLGdCQUFnQixDQUFDQyxNQUFNLEdBQUcsR0FBRztZQUMzRVA7UUFDRjtRQUVBLDhCQUE4QjtRQUM5QixJQUFJRCxZQUFZUyxRQUFRLElBQUlULFlBQVlTLFFBQVEsQ0FBQ0QsTUFBTSxHQUFHLEdBQUc7WUFDM0QsTUFBTUUsbUJBQW1CVixZQUFZUyxRQUFRLENBQUNFLElBQUksQ0FBQ0MsQ0FBQUEsSUFDakRBLEVBQUVDLFdBQVcsSUFBSUQsRUFBRUUsU0FBUyxJQUFJRixFQUFFRyxPQUFPLElBQUlILEVBQUVJLFdBQVc7WUFFNUQsSUFBSU4sa0JBQWtCO2dCQUNwQlQ7WUFDRjtRQUNGO1FBRUEsMEJBQTBCO1FBQzFCLElBQUlELFlBQVlpQixTQUFTLElBQUlqQixZQUFZaUIsU0FBUyxDQUFDVCxNQUFNLEdBQUcsR0FBRztZQUM3RCxNQUFNVSxrQkFBa0JsQixZQUFZaUIsU0FBUyxDQUFDTixJQUFJLENBQUNRLENBQUFBLElBQUtBLEVBQUV6QyxVQUFVO1lBQ3BFLElBQUl3QyxpQkFBaUI7Z0JBQ25CakI7WUFDRjtRQUNGO1FBRUEsK0RBQStEO1FBQy9ELElBQUlELFlBQVk5QyxNQUFNLEtBQUssU0FBUztZQUNsQytDO1FBQ0Y7UUFFQSxPQUFPbUIsS0FBS0MsS0FBSyxDQUFDLGlCQUFrQm5CLGFBQWM7SUFDcEQ7QUFDRixFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXExFTk9WT1xcRGVza3RvcFxcQ09ERVxcV09SS1xcTW9IXFxNb1VcXGNsaWVudFxcbGliXFxzZXJ2aWNlc1xcbW91LWFwcGxpY2F0aW9uLnNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGFwaSBmcm9tIFwiLi4vYXBpXCJcblxuZXhwb3J0IHR5cGUgQXBwbGljYXRpb25TdGF0dXMgPSAnRFJBRlQnIHwgJ1NVQk1JVFRFRCcgfCAnVU5ERVJfUkVWSUVXJyB8ICdBUFBST1ZFRCcgfCAnUkVKRUNURUQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgTW91QXBwbGljYXRpb24ge1xuICBpZDogc3RyaW5nXG4gIG1vdUFwcGxpY2F0aW9uSWQ6IHN0cmluZ1xuICBtb3VJZDogc3RyaW5nXG4gIHJlc3BvbnNpYmlsaXR5Pzogc3RyaW5nXG5cbiAgLy8gRW5oYW5jZWQgd29ya2Zsb3cgZmllbGRzXG4gIHN0YXR1czogQXBwbGljYXRpb25TdGF0dXNcbiAgY3VycmVudFN0ZXA6IG51bWJlclxuICBtb3VEdXJhdGlvblllYXJzPzogbnVtYmVyXG4gIHNpZ25hdG9yeU5hbWU/OiBzdHJpbmdcbiAgc2lnbmF0b3J5UG9zaXRpb24/OiBzdHJpbmdcbiAgcGFydHlOYW1lPzogc3RyaW5nXG4gIGNvbXBsZXRpb25QZXJjZW50YWdlOiBudW1iZXJcbiAgbGFzdEF1dG9TYXZlPzogc3RyaW5nXG5cbiAgdXNlcklkPzogc3RyaW5nXG4gIHVzZXI/OiB7XG4gICAgaWQ6IHN0cmluZ1xuICAgIGZpcnN0TmFtZTogc3RyaW5nXG4gICAgbGFzdE5hbWU6IHN0cmluZ1xuICAgIGVtYWlsOiBzdHJpbmdcbiAgICBvcmdhbml6YXRpb24/OiB7XG4gICAgICBpZDogc3RyaW5nXG4gICAgICBvcmdhbml6YXRpb25OYW1lOiBzdHJpbmdcbiAgICB9XG4gIH1cbiAgbW91Pzoge1xuICAgIGlkOiBzdHJpbmdcbiAgICBtb3VJZDogc3RyaW5nXG4gICAgcGFydHk6IHtcbiAgICAgIGlkOiBzdHJpbmdcbiAgICAgIHBhcnR5TmFtZTogc3RyaW5nXG4gICAgfVxuICB9XG4gIHByb2plY3RzPzogUHJvamVjdFtdXG4gIHJlc3BvbnNpYmlsaXRpZXM/OiBBcHBsaWNhdGlvblJlc3BvbnNpYmlsaXR5W11cbiAgZG9jdW1lbnRzPzogQXBwbGljYXRpb25Eb2N1bWVudFtdXG4gIGFwcHJvdmFsU3RlcHM/OiBBcHByb3ZhbFN0ZXBbXVxuICBjcmVhdGVkQXQ6IHN0cmluZ1xuICB1cGRhdGVkQXQ6IHN0cmluZ1xuICBkZWxldGVkOiBib29sZWFuXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUHJvamVjdCB7XG4gIGlkOiBzdHJpbmdcbiAgcHJvamVjdElkOiBzdHJpbmdcbiAgcHJvamVjdE5hbWU6IHN0cmluZ1xuICBwcm9qZWN0RGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgc3RhcnREYXRlPzogc3RyaW5nXG4gIGVuZERhdGU/OiBzdHJpbmdcbiAgdG90YWxCdWRnZXQ/OiBudW1iZXJcbiAgY3VycmVuY3k/OiBzdHJpbmdcbiAgc3RhdHVzPzogc3RyaW5nXG4gIG1vdUFwcGxpY2F0aW9uSWQ6IHN0cmluZ1xuICBhY3Rpdml0aWVzPzogUHJvamVjdEFjdGl2aXR5W11cbiAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgdXBkYXRlZEF0OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBBcHBsaWNhdGlvblJlc3BvbnNpYmlsaXR5IHtcbiAgaWQ6IHN0cmluZ1xuICByZXNwb25zaWJpbGl0eVRleHQ6IHN0cmluZ1xuICBvcmRlcjogbnVtYmVyXG4gIG1vdUFwcGxpY2F0aW9uSWQ6IHN0cmluZ1xuICBjcmVhdGVkQXQ6IHN0cmluZ1xuICB1cGRhdGVkQXQ6IHN0cmluZ1xuICBkZWxldGVkOiBib29sZWFuXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXBwbGljYXRpb25Eb2N1bWVudCB7XG4gIGlkOiBzdHJpbmdcbiAgZG9jdW1lbnRJZDogc3RyaW5nXG4gIGZpbGVOYW1lOiBzdHJpbmdcbiAgb3JpZ2luYWxOYW1lOiBzdHJpbmdcbiAgbWltZVR5cGU6IHN0cmluZ1xuICBmaWxlU2l6ZTogbnVtYmVyXG4gIGZpbGVQYXRoOiBzdHJpbmdcbiAgZG9jdW1lbnRUeXBlOiBzdHJpbmdcbiAgaXNSZXF1aXJlZDogYm9vbGVhblxuICBtb3VBcHBsaWNhdGlvbklkOiBzdHJpbmdcbiAgdXBsb2FkZWRBdDogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG4gIGRlbGV0ZWQ6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBQcm9qZWN0QWN0aXZpdHkge1xuICBpZDogc3RyaW5nXG4gIGFjdGl2aXR5SWQ6IHN0cmluZ1xuICBhY3Rpdml0eU5hbWU6IHN0cmluZ1xuICBkZXNjcmlwdGlvbj86IHN0cmluZ1xuICB0aW1lbGluZT86IHN0cmluZ1xuICBidWRnZXRBbGxvY2F0aW9uPzogbnVtYmVyXG4gIGN1cnJlbmN5Pzogc3RyaW5nXG4gIHByb2plY3RJZDogc3RyaW5nXG4gIGNyZWF0ZWRBdDogc3RyaW5nXG4gIHVwZGF0ZWRBdDogc3RyaW5nXG4gIGRlbGV0ZWQ6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBBcHByb3ZhbFN0ZXAge1xuICBpZDogc3RyaW5nXG4gIHN0ZXBOdW1iZXI6IG51bWJlclxuICByZXZpZXdlclJvbGU6IHN0cmluZ1xuICByZXZpZXdlcklkPzogc3RyaW5nXG4gIHJldmlld2VyPzoge1xuICAgIGlkOiBzdHJpbmdcbiAgICBmaXJzdE5hbWU6IHN0cmluZ1xuICAgIGxhc3ROYW1lOiBzdHJpbmdcbiAgICBlbWFpbDogc3RyaW5nXG4gIH1cbiAgc3RhdHVzOiAnUEVORElORycgfCAnUkVDT01NRU5EX0ZPUl9BUFBST1ZBTCcgfCAnUkVDT01NRU5EX0ZPUl9NT0RJRklDQVRJT04nIHwgJ1JFSkVDVCcgfCAnQVBQUk9WRSdcbiAgY29tbWVudHM/OiBzdHJpbmdcbiAgcmV2aWV3ZWRBdD86IHN0cmluZ1xuICBtb3VBcHBsaWNhdGlvbklkOiBzdHJpbmdcbiAgY3JlYXRlZEF0OiBzdHJpbmdcbiAgdXBkYXRlZEF0OiBzdHJpbmdcbn1cblxuLy8gTXVsdGktc3RlcCBmb3JtIGRhdGEgaW50ZXJmYWNlc1xuZXhwb3J0IGludGVyZmFjZSBTdGVwMURhdGEge1xuICBtb3VEdXJhdGlvblllYXJzOiBudW1iZXJcbiAgb3JnYW5pemF0aW9uTmFtZTogc3RyaW5nIC8vIHJlYWQtb25seSwgZnJvbSB1c2VyJ3Mgb3JnYW5pemF0aW9uXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3RlcDJEYXRhIHtcbiAgcGFydHlOYW1lOiBzdHJpbmdcbiAgc2lnbmF0b3J5TmFtZTogc3RyaW5nXG4gIHNpZ25hdG9yeVBvc2l0aW9uOiBzdHJpbmdcbiAgcmVzcG9uc2liaWxpdGllczogc3RyaW5nW11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdGVwM0RhdGEge1xuICBwcm9qZWN0czogUHJvamVjdEZvcm1EYXRhW11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBQcm9qZWN0Rm9ybURhdGEge1xuICBpZD86IHN0cmluZ1xuICBwcm9qZWN0TmFtZTogc3RyaW5nXG4gIHByb2plY3REZXNjcmlwdGlvbj86IHN0cmluZ1xuICBzdGFydERhdGU/OiBzdHJpbmdcbiAgZW5kRGF0ZT86IHN0cmluZ1xuICB0b3RhbEJ1ZGdldD86IG51bWJlclxuICBjdXJyZW5jeT86IHN0cmluZ1xuICBhY3Rpdml0aWVzOiBBY3Rpdml0eUZvcm1EYXRhW11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBBY3Rpdml0eUZvcm1EYXRhIHtcbiAgaWQ/OiBzdHJpbmdcbiAgYWN0aXZpdHlOYW1lOiBzdHJpbmdcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgdGltZWxpbmU/OiBzdHJpbmdcbiAgYnVkZ2V0QWxsb2NhdGlvbj86IG51bWJlclxuICBjdXJyZW5jeT86IHN0cmluZ1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0ZXA0RGF0YSB7XG4gIGRvY3VtZW50czogRG9jdW1lbnRVcGxvYWREYXRhW11cbn1cblxuZXhwb3J0IGludGVyZmFjZSBEb2N1bWVudFVwbG9hZERhdGEge1xuICBmaWxlOiBGaWxlXG4gIGRvY3VtZW50VHlwZTogc3RyaW5nXG4gIGlzUmVxdWlyZWQ6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBTdGVwNURhdGEge1xuICB0ZXJtc0FjY2VwdGVkOiBib29sZWFuXG4gIGZpbmFsUmV2aWV3OiBib29sZWFuXG59XG5cbi8vIExlZ2FjeSBpbnRlcmZhY2VzIGZvciBiYWNrd2FyZCBjb21wYXRpYmlsaXR5XG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZU1vdUFwcGxpY2F0aW9uUmVxdWVzdCB7XG4gIG1vdUlkOiBzdHJpbmdcbiAgcmVzcG9uc2liaWxpdHk/OiBzdHJpbmdcbiAgcHJvamVjdHM/OiBDcmVhdGVQcm9qZWN0UmVxdWVzdFtdXG5cbiAgLy8gRW5oYW5jZWQgZmllbGRzXG4gIHN0YXR1cz86IEFwcGxpY2F0aW9uU3RhdHVzXG4gIGN1cnJlbnRTdGVwPzogbnVtYmVyXG4gIG1vdUR1cmF0aW9uWWVhcnM/OiBudW1iZXJcbiAgc2lnbmF0b3J5TmFtZT86IHN0cmluZ1xuICBzaWduYXRvcnlQb3NpdGlvbj86IHN0cmluZ1xuICBwYXJ0eU5hbWU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDcmVhdGVQcm9qZWN0UmVxdWVzdCB7XG4gIHByb2plY3ROYW1lOiBzdHJpbmdcbiAgcHJvamVjdERlc2NyaXB0aW9uPzogc3RyaW5nXG4gIHN0YXJ0RGF0ZT86IHN0cmluZ1xuICBlbmREYXRlPzogc3RyaW5nXG4gIHRvdGFsQnVkZ2V0PzogbnVtYmVyXG4gIGN1cnJlbmN5Pzogc3RyaW5nXG4gIGFjdGl2aXRpZXM/OiBDcmVhdGVBY3Rpdml0eVJlcXVlc3RbXVxufVxuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZUFjdGl2aXR5UmVxdWVzdCB7XG4gIGFjdGl2aXR5TmFtZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nXG4gIHRpbWVsaW5lPzogc3RyaW5nXG4gIGJ1ZGdldEFsbG9jYXRpb24/OiBudW1iZXJcbiAgY3VycmVuY3k/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBVcGRhdGVNb3VBcHBsaWNhdGlvblJlcXVlc3Qge1xuICByZXNwb25zaWJpbGl0eT86IHN0cmluZ1xuICBwcm9qZWN0cz86IFVwZGF0ZVByb2plY3RSZXF1ZXN0W11cblxuICAvLyBFbmhhbmNlZCBmaWVsZHNcbiAgc3RhdHVzPzogQXBwbGljYXRpb25TdGF0dXNcbiAgY3VycmVudFN0ZXA/OiBudW1iZXJcbiAgbW91RHVyYXRpb25ZZWFycz86IG51bWJlclxuICBzaWduYXRvcnlOYW1lPzogc3RyaW5nXG4gIHNpZ25hdG9yeVBvc2l0aW9uPzogc3RyaW5nXG4gIHBhcnR5TmFtZT86IHN0cmluZ1xuICBjb21wbGV0aW9uUGVyY2VudGFnZT86IG51bWJlclxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVByb2plY3RSZXF1ZXN0IHtcbiAgaWQ/OiBzdHJpbmdcbiAgcHJvamVjdE5hbWU6IHN0cmluZ1xuICBwcm9qZWN0RGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgc3RhcnREYXRlPzogc3RyaW5nXG4gIGVuZERhdGU/OiBzdHJpbmdcbiAgdG90YWxCdWRnZXQ/OiBudW1iZXJcbiAgY3VycmVuY3k/OiBzdHJpbmdcbiAgYWN0aXZpdGllcz86IFVwZGF0ZUFjdGl2aXR5UmVxdWVzdFtdXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXBkYXRlQWN0aXZpdHlSZXF1ZXN0IHtcbiAgaWQ/OiBzdHJpbmdcbiAgYWN0aXZpdHlOYW1lOiBzdHJpbmdcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmdcbiAgdGltZWxpbmU/OiBzdHJpbmdcbiAgYnVkZ2V0QWxsb2NhdGlvbj86IG51bWJlclxuICBjdXJyZW5jeT86IHN0cmluZ1xufVxuXG4vLyBTdGVwLXNwZWNpZmljIHVwZGF0ZSBpbnRlcmZhY2VzXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVN0ZXBSZXF1ZXN0IHtcbiAgc3RlcE51bWJlcjogbnVtYmVyXG4gIGRhdGE6IGFueVxuICBhdXRvU2F2ZT86IGJvb2xlYW5cbn1cblxuZXhwb3J0IGNvbnN0IG1vdUFwcGxpY2F0aW9uU2VydmljZSA9IHtcbiAgLy8gR2V0IGFsbCBNb1UgYXBwbGljYXRpb25zIGZvciB0aGUgY3VycmVudCB1c2VyIChwYXJ0bmVyKVxuICBhc3luYyBnZXRNeUFwcGxpY2F0aW9ucygpOiBQcm9taXNlPE1vdUFwcGxpY2F0aW9uW10+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoXCIvbW91LWFwcGxpY2F0aW9ucy9teS1hcHBsaWNhdGlvbnNcIilcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIEdldCBhbGwgTW9VIGFwcGxpY2F0aW9ucyAoYWRtaW4vcmV2aWV3ZXIgYWNjZXNzKVxuICBhc3luYyBnZXRBbGxBcHBsaWNhdGlvbnMoKTogUHJvbWlzZTxNb3VBcHBsaWNhdGlvbltdPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZ2V0KFwiL21vdS1hcHBsaWNhdGlvbnNcIilcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIEdldCBhIHNwZWNpZmljIE1vVSBhcHBsaWNhdGlvbiBieSBJRFxuICBhc3luYyBnZXRBcHBsaWNhdGlvbihpZDogc3RyaW5nKTogUHJvbWlzZTxNb3VBcHBsaWNhdGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmdldChgL21vdS1hcHBsaWNhdGlvbnMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgLy8gQ3JlYXRlIGEgbmV3IE1vVSBhcHBsaWNhdGlvbiAoZHJhZnQgYnkgZGVmYXVsdClcbiAgYXN5bmMgY3JlYXRlQXBwbGljYXRpb24oZGF0YTogQ3JlYXRlTW91QXBwbGljYXRpb25SZXF1ZXN0KTogUHJvbWlzZTxNb3VBcHBsaWNhdGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoXCIvbW91LWFwcGxpY2F0aW9uc1wiLCB7XG4gICAgICAuLi5kYXRhLFxuICAgICAgc3RhdHVzOiBkYXRhLnN0YXR1cyB8fCAnRFJBRlQnLFxuICAgICAgY3VycmVudFN0ZXA6IGRhdGEuY3VycmVudFN0ZXAgfHwgMVxuICAgIH0pXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICAvLyBDcmVhdGUgYSBkcmFmdCBhcHBsaWNhdGlvbiB3aXRoIG1pbmltYWwgZGF0YVxuICBhc3luYyBjcmVhdGVEcmFmdChtb3VJZDogc3RyaW5nKTogUHJvbWlzZTxNb3VBcHBsaWNhdGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoXCIvbW91LWFwcGxpY2F0aW9ucy9kcmFmdFwiLCB7XG4gICAgICBtb3VJZCxcbiAgICAgIHN0YXR1czogJ0RSQUZUJyxcbiAgICAgIGN1cnJlbnRTdGVwOiAxLFxuICAgICAgY29tcGxldGlvblBlcmNlbnRhZ2U6IDBcbiAgICB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgLy8gVXBkYXRlIGFuIGV4aXN0aW5nIE1vVSBhcHBsaWNhdGlvblxuICBhc3luYyB1cGRhdGVBcHBsaWNhdGlvbihpZDogc3RyaW5nLCBkYXRhOiBVcGRhdGVNb3VBcHBsaWNhdGlvblJlcXVlc3QpOiBQcm9taXNlPE1vdUFwcGxpY2F0aW9uPiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucHV0KGAvbW91LWFwcGxpY2F0aW9ucy8ke2lkfWAsIGRhdGEpXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICAvLyBVcGRhdGUgYSBzcGVjaWZpYyBzdGVwIG9mIHRoZSBhcHBsaWNhdGlvblxuICBhc3luYyB1cGRhdGVTdGVwKGlkOiBzdHJpbmcsIHN0ZXBOdW1iZXI6IG51bWJlciwgZGF0YTogYW55LCBhdXRvU2F2ZSA9IGZhbHNlKTogUHJvbWlzZTxNb3VBcHBsaWNhdGlvbj4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBhdGNoKGAvbW91LWFwcGxpY2F0aW9ucy8ke2lkfS9zdGVwLyR7c3RlcE51bWJlcn1gLCB7XG4gICAgICBkYXRhLFxuICAgICAgYXV0b1NhdmUsXG4gICAgICBsYXN0QXV0b1NhdmU6IGF1dG9TYXZlID8gbmV3IERhdGUoKS50b0lTT1N0cmluZygpIDogdW5kZWZpbmVkXG4gICAgfSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIEF1dG8tc2F2ZSBhcHBsaWNhdGlvbiBkYXRhXG4gIGFzeW5jIGF1dG9TYXZlKGlkOiBzdHJpbmcsIGRhdGE6IGFueSk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9tb3UtYXBwbGljYXRpb25zLyR7aWR9L2F1dG8tc2F2ZWAsIHtcbiAgICAgIC4uLmRhdGEsXG4gICAgICBsYXN0QXV0b1NhdmU6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH0pXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICAvLyBEZWxldGUgYSBNb1UgYXBwbGljYXRpb25cbiAgYXN5bmMgZGVsZXRlQXBwbGljYXRpb24oaWQ6IHN0cmluZyk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLmRlbGV0ZShgL21vdS1hcHBsaWNhdGlvbnMvJHtpZH1gKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgLy8gU3VibWl0IGFwcGxpY2F0aW9uIGZvciByZXZpZXcgKGNvbnZlcnQgZnJvbSBEUkFGVCB0byBTVUJNSVRURUQpXG4gIGFzeW5jIHN1Ym1pdEFwcGxpY2F0aW9uKGlkOiBzdHJpbmcpOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5wb3N0KGAvbW91LWFwcGxpY2F0aW9ucy8ke2lkfS9zdWJtaXRgKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgLy8gR2V0IGF2YWlsYWJsZSBNb1VzIGZvciBhcHBsaWNhdGlvblxuICBhc3luYyBnZXRBdmFpbGFibGVNb3VzKCk6IFByb21pc2U8YW55W10+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoXCIvbW91cy9hdmFpbGFibGVcIilcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIEdldCBhcHBsaWNhdGlvbiBzdGF0aXN0aWNzIGZvciBkYXNoYm9hcmRcbiAgYXN5bmMgZ2V0QXBwbGljYXRpb25TdGF0cygpOiBQcm9taXNlPHtcbiAgICB0b3RhbDogbnVtYmVyXG4gICAgcGVuZGluZzogbnVtYmVyXG4gICAgYXBwcm92ZWQ6IG51bWJlclxuICAgIHJlamVjdGVkOiBudW1iZXJcbiAgICBkcmFmdDogbnVtYmVyXG4gIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5nZXQoXCIvbW91LWFwcGxpY2F0aW9ucy9zdGF0c1wiKVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgLy8gRG9jdW1lbnQgdXBsb2FkIG1ldGhvZHNcbiAgYXN5bmMgdXBsb2FkRG9jdW1lbnQoYXBwbGljYXRpb25JZDogc3RyaW5nLCBmaWxlOiBGaWxlLCBkb2N1bWVudFR5cGU6IHN0cmluZywgaXNSZXF1aXJlZCA9IGZhbHNlKTogUHJvbWlzZTxBcHBsaWNhdGlvbkRvY3VtZW50PiB7XG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKVxuICAgIGZvcm1EYXRhLmFwcGVuZCgnZmlsZScsIGZpbGUpXG4gICAgZm9ybURhdGEuYXBwZW5kKCdkb2N1bWVudFR5cGUnLCBkb2N1bWVudFR5cGUpXG4gICAgZm9ybURhdGEuYXBwZW5kKCdpc1JlcXVpcmVkJywgaXNSZXF1aXJlZC50b1N0cmluZygpKVxuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkucG9zdChgL21vdS1hcHBsaWNhdGlvbnMvJHthcHBsaWNhdGlvbklkfS9kb2N1bWVudHNgLCBmb3JtRGF0YSwge1xuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ211bHRpcGFydC9mb3JtLWRhdGEnXG4gICAgICB9XG4gICAgfSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGFzeW5jIGRlbGV0ZURvY3VtZW50KGFwcGxpY2F0aW9uSWQ6IHN0cmluZywgZG9jdW1lbnRJZDogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZGVsZXRlKGAvbW91LWFwcGxpY2F0aW9ucy8ke2FwcGxpY2F0aW9uSWR9L2RvY3VtZW50cy8ke2RvY3VtZW50SWR9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIFJlc3BvbnNpYmlsaXR5IG1hbmFnZW1lbnRcbiAgYXN5bmMgYWRkUmVzcG9uc2liaWxpdHkoYXBwbGljYXRpb25JZDogc3RyaW5nLCByZXNwb25zaWJpbGl0eVRleHQ6IHN0cmluZywgb3JkZXI6IG51bWJlcik6IFByb21pc2U8QXBwbGljYXRpb25SZXNwb25zaWJpbGl0eT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9tb3UtYXBwbGljYXRpb25zLyR7YXBwbGljYXRpb25JZH0vcmVzcG9uc2liaWxpdGllc2AsIHtcbiAgICAgIHJlc3BvbnNpYmlsaXR5VGV4dCxcbiAgICAgIG9yZGVyXG4gICAgfSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGFzeW5jIHVwZGF0ZVJlc3BvbnNpYmlsaXR5KGFwcGxpY2F0aW9uSWQ6IHN0cmluZywgcmVzcG9uc2liaWxpdHlJZDogc3RyaW5nLCByZXNwb25zaWJpbGl0eVRleHQ6IHN0cmluZyk6IFByb21pc2U8QXBwbGljYXRpb25SZXNwb25zaWJpbGl0eT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnB1dChgL21vdS1hcHBsaWNhdGlvbnMvJHthcHBsaWNhdGlvbklkfS9yZXNwb25zaWJpbGl0aWVzLyR7cmVzcG9uc2liaWxpdHlJZH1gLCB7XG4gICAgICByZXNwb25zaWJpbGl0eVRleHRcbiAgICB9KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgYXN5bmMgZGVsZXRlUmVzcG9uc2liaWxpdHkoYXBwbGljYXRpb25JZDogc3RyaW5nLCByZXNwb25zaWJpbGl0eUlkOiBzdHJpbmcpOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaS5kZWxldGUoYC9tb3UtYXBwbGljYXRpb25zLyR7YXBwbGljYXRpb25JZH0vcmVzcG9uc2liaWxpdGllcy8ke3Jlc3BvbnNpYmlsaXR5SWR9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIFByb2plY3QgYWN0aXZpdHkgbWFuYWdlbWVudFxuICBhc3luYyBhZGRQcm9qZWN0QWN0aXZpdHkocHJvamVjdElkOiBzdHJpbmcsIGFjdGl2aXR5OiBDcmVhdGVBY3Rpdml0eVJlcXVlc3QpOiBQcm9taXNlPFByb2plY3RBY3Rpdml0eT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9wcm9qZWN0cy8ke3Byb2plY3RJZH0vYWN0aXZpdGllc2AsIGFjdGl2aXR5KVxuICAgIHJldHVybiByZXNwb25zZS5kYXRhXG4gIH0sXG5cbiAgYXN5bmMgdXBkYXRlUHJvamVjdEFjdGl2aXR5KHByb2plY3RJZDogc3RyaW5nLCBhY3Rpdml0eUlkOiBzdHJpbmcsIGFjdGl2aXR5OiBVcGRhdGVBY3Rpdml0eVJlcXVlc3QpOiBQcm9taXNlPFByb2plY3RBY3Rpdml0eT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnB1dChgL3Byb2plY3RzLyR7cHJvamVjdElkfS9hY3Rpdml0aWVzLyR7YWN0aXZpdHlJZH1gLCBhY3Rpdml0eSlcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIGFzeW5jIGRlbGV0ZVByb2plY3RBY3Rpdml0eShwcm9qZWN0SWQ6IHN0cmluZywgYWN0aXZpdHlJZDogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGkuZGVsZXRlKGAvcHJvamVjdHMvJHtwcm9qZWN0SWR9L2FjdGl2aXRpZXMvJHthY3Rpdml0eUlkfWApXG4gICAgcmV0dXJuIHJlc3BvbnNlLmRhdGFcbiAgfSxcblxuICAvLyBWYWxpZGF0aW9uIG1ldGhvZHNcbiAgYXN5bmMgdmFsaWRhdGVTdGVwKGFwcGxpY2F0aW9uSWQ6IHN0cmluZywgc3RlcE51bWJlcjogbnVtYmVyKTogUHJvbWlzZTx7IGlzVmFsaWQ6IGJvb2xlYW47IGVycm9yczogc3RyaW5nW10gfT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpLnBvc3QoYC9tb3UtYXBwbGljYXRpb25zLyR7YXBwbGljYXRpb25JZH0vdmFsaWRhdGUtc3RlcC8ke3N0ZXBOdW1iZXJ9YClcbiAgICByZXR1cm4gcmVzcG9uc2UuZGF0YVxuICB9LFxuXG4gIC8vIFByb2dyZXNzIGNhbGN1bGF0aW9uXG4gIGNhbGN1bGF0ZUNvbXBsZXRpb25QZXJjZW50YWdlKGFwcGxpY2F0aW9uOiBNb3VBcHBsaWNhdGlvbik6IG51bWJlciB7XG4gICAgbGV0IGNvbXBsZXRlZFN0ZXBzID0gMFxuICAgIGNvbnN0IHRvdGFsU3RlcHMgPSA1XG5cbiAgICAvLyBTdGVwIDE6IE1vVSBJbmZvcm1hdGlvblxuICAgIGlmIChhcHBsaWNhdGlvbi5tb3VEdXJhdGlvblllYXJzKSB7XG4gICAgICBjb21wbGV0ZWRTdGVwcysrXG4gICAgfVxuXG4gICAgLy8gU3RlcCAyOiBQYXJ0eSBEZXRhaWxzXG4gICAgaWYgKGFwcGxpY2F0aW9uLnBhcnR5TmFtZSAmJiBhcHBsaWNhdGlvbi5zaWduYXRvcnlOYW1lICYmIGFwcGxpY2F0aW9uLnNpZ25hdG9yeVBvc2l0aW9uICYmXG4gICAgICAgIGFwcGxpY2F0aW9uLnJlc3BvbnNpYmlsaXRpZXMgJiYgYXBwbGljYXRpb24ucmVzcG9uc2liaWxpdGllcy5sZW5ndGggPiAwKSB7XG4gICAgICBjb21wbGV0ZWRTdGVwcysrXG4gICAgfVxuXG4gICAgLy8gU3RlcCAzOiBQcm9qZWN0IEluZm9ybWF0aW9uXG4gICAgaWYgKGFwcGxpY2F0aW9uLnByb2plY3RzICYmIGFwcGxpY2F0aW9uLnByb2plY3RzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IGhhc1ZhbGlkUHJvamVjdHMgPSBhcHBsaWNhdGlvbi5wcm9qZWN0cy5zb21lKHAgPT5cbiAgICAgICAgcC5wcm9qZWN0TmFtZSAmJiBwLnN0YXJ0RGF0ZSAmJiBwLmVuZERhdGUgJiYgcC50b3RhbEJ1ZGdldFxuICAgICAgKVxuICAgICAgaWYgKGhhc1ZhbGlkUHJvamVjdHMpIHtcbiAgICAgICAgY29tcGxldGVkU3RlcHMrK1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFN0ZXAgNDogRG9jdW1lbnQgVXBsb2FkXG4gICAgaWYgKGFwcGxpY2F0aW9uLmRvY3VtZW50cyAmJiBhcHBsaWNhdGlvbi5kb2N1bWVudHMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgaGFzUmVxdWlyZWREb2NzID0gYXBwbGljYXRpb24uZG9jdW1lbnRzLnNvbWUoZCA9PiBkLmlzUmVxdWlyZWQpXG4gICAgICBpZiAoaGFzUmVxdWlyZWREb2NzKSB7XG4gICAgICAgIGNvbXBsZXRlZFN0ZXBzKytcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBTdGVwIDU6IFJldmlldyAmIFN1Ym1pdCAoY29tcGxldGVkIHdoZW4gc3RhdHVzIGlzIG5vdCBEUkFGVClcbiAgICBpZiAoYXBwbGljYXRpb24uc3RhdHVzICE9PSAnRFJBRlQnKSB7XG4gICAgICBjb21wbGV0ZWRTdGVwcysrXG4gICAgfVxuXG4gICAgcmV0dXJuIE1hdGgucm91bmQoKGNvbXBsZXRlZFN0ZXBzIC8gdG90YWxTdGVwcykgKiAxMDApXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJhcGkiLCJtb3VBcHBsaWNhdGlvblNlcnZpY2UiLCJnZXRNeUFwcGxpY2F0aW9ucyIsInJlc3BvbnNlIiwiZ2V0IiwiZGF0YSIsImdldEFsbEFwcGxpY2F0aW9ucyIsImdldEFwcGxpY2F0aW9uIiwiaWQiLCJjcmVhdGVBcHBsaWNhdGlvbiIsInBvc3QiLCJzdGF0dXMiLCJjdXJyZW50U3RlcCIsImNyZWF0ZURyYWZ0IiwibW91SWQiLCJjb21wbGV0aW9uUGVyY2VudGFnZSIsInVwZGF0ZUFwcGxpY2F0aW9uIiwicHV0IiwidXBkYXRlU3RlcCIsInN0ZXBOdW1iZXIiLCJhdXRvU2F2ZSIsInBhdGNoIiwibGFzdEF1dG9TYXZlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidW5kZWZpbmVkIiwiZGVsZXRlQXBwbGljYXRpb24iLCJkZWxldGUiLCJzdWJtaXRBcHBsaWNhdGlvbiIsImdldEF2YWlsYWJsZU1vdXMiLCJnZXRBcHBsaWNhdGlvblN0YXRzIiwidXBsb2FkRG9jdW1lbnQiLCJhcHBsaWNhdGlvbklkIiwiZmlsZSIsImRvY3VtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJmb3JtRGF0YSIsIkZvcm1EYXRhIiwiYXBwZW5kIiwidG9TdHJpbmciLCJoZWFkZXJzIiwiZGVsZXRlRG9jdW1lbnQiLCJkb2N1bWVudElkIiwiYWRkUmVzcG9uc2liaWxpdHkiLCJyZXNwb25zaWJpbGl0eVRleHQiLCJvcmRlciIsInVwZGF0ZVJlc3BvbnNpYmlsaXR5IiwicmVzcG9uc2liaWxpdHlJZCIsImRlbGV0ZVJlc3BvbnNpYmlsaXR5IiwiYWRkUHJvamVjdEFjdGl2aXR5IiwicHJvamVjdElkIiwiYWN0aXZpdHkiLCJ1cGRhdGVQcm9qZWN0QWN0aXZpdHkiLCJhY3Rpdml0eUlkIiwiZGVsZXRlUHJvamVjdEFjdGl2aXR5IiwidmFsaWRhdGVTdGVwIiwiY2FsY3VsYXRlQ29tcGxldGlvblBlcmNlbnRhZ2UiLCJhcHBsaWNhdGlvbiIsImNvbXBsZXRlZFN0ZXBzIiwidG90YWxTdGVwcyIsIm1vdUR1cmF0aW9uWWVhcnMiLCJwYXJ0eU5hbWUiLCJzaWduYXRvcnlOYW1lIiwic2lnbmF0b3J5UG9zaXRpb24iLCJyZXNwb25zaWJpbGl0aWVzIiwibGVuZ3RoIiwicHJvamVjdHMiLCJoYXNWYWxpZFByb2plY3RzIiwic29tZSIsInAiLCJwcm9qZWN0TmFtZSIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJ0b3RhbEJ1ZGdldCIsImRvY3VtZW50cyIsImhhc1JlcXVpcmVkRG9jcyIsImQiLCJNYXRoIiwicm91bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/mou-application.service.ts\n"));

/***/ })

});