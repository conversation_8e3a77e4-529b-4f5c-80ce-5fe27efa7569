"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CurrenciesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrenciesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let CurrenciesService = CurrenciesService_1 = class CurrenciesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(CurrenciesService_1.name);
    }
    async findAll() {
        try {
            const currencies = await this.prisma.currency.findMany({
                where: { deleted: false },
                orderBy: { createdAt: 'desc' }
            });
            return currencies;
        }
        catch (error) {
            this.logger.error('Failed to fetch currencies', error.stack);
            throw new Error('Failed to fetch currencies');
        }
    }
    async findOne(id) {
        try {
            const currency = await this.prisma.currency.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!currency) {
                throw new common_1.NotFoundException('Currency not found');
            }
            return currency;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error('Failed to fetch currency', error.stack);
            throw new Error('Failed to fetch currency');
        }
    }
    async create(createCurrencyDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create currencies');
            }
            const existingCurrency = await this.prisma.currency.findFirst({
                where: {
                    currencyCode: createCurrencyDto.currencyCode.toUpperCase(),
                    deleted: false
                }
            });
            if (existingCurrency) {
                throw new common_1.ConflictException('Currency with this code already exists');
            }
            const currency = await this.prisma.currency.create({
                data: {
                    currencyCode: createCurrencyDto.currencyCode.toUpperCase(),
                    currencyName: createCurrencyDto.currencyName
                }
            });
            return currency;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create currency', error.stack);
            throw new Error('Failed to create currency');
        }
    }
    async update(id, updateCurrencyDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can update currencies');
            }
            const existingCurrency = await this.prisma.currency.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingCurrency) {
                throw new common_1.NotFoundException('Currency not found');
            }
            if (updateCurrencyDto.currencyCode && updateCurrencyDto.currencyCode.toUpperCase() !== existingCurrency.currencyCode) {
                const codeConflict = await this.prisma.currency.findFirst({
                    where: {
                        currencyCode: updateCurrencyDto.currencyCode.toUpperCase(),
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (codeConflict) {
                    throw new common_1.ConflictException('Currency with this code already exists');
                }
            }
            const updateData = {};
            if (updateCurrencyDto.currencyCode) {
                updateData.currencyCode = updateCurrencyDto.currencyCode.toUpperCase();
            }
            if (updateCurrencyDto.currencyName) {
                updateData.currencyName = updateCurrencyDto.currencyName;
            }
            const currency = await this.prisma.currency.update({
                where: { id },
                data: updateData
            });
            return currency;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to update currency', error.stack);
            throw new Error('Failed to update currency');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete currencies');
            }
            const existingCurrency = await this.prisma.currency.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingCurrency) {
                throw new common_1.NotFoundException('Currency not found');
            }
            await this.prisma.currency.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'Currency deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to delete currency', error.stack);
            throw new Error('Failed to delete currency');
        }
    }
};
exports.CurrenciesService = CurrenciesService;
exports.CurrenciesService = CurrenciesService = CurrenciesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CurrenciesService);
//# sourceMappingURL=currencies.service.js.map