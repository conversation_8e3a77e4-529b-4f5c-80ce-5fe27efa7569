import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail, IsOptional, IsInt, ValidateNested, IsArray, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateAddressDto, AddressResponseDto } from '../../addresses/dto';

export class CreateOrganizationDto {
    @ApiProperty({ description: 'Organization name' })
    @IsNotEmpty()
    @IsString()
    organizationName: string;

    @ApiProperty({ description: 'Organization business registration number' })
    @IsNotEmpty()
    @IsString()
    organizationRegistrationNumber: string;

    @ApiProperty({ description: 'Organization phone number' })
    @IsNotEmpty()
    @IsString()
    organizationPhoneNumber: string;

    @ApiProperty({ description: 'Organization email address' })
    @IsNotEmpty()
    @IsEmail()
    organizationEmail: string;

    @ApiProperty({ required: false, description: 'Organization website URL' })
    @IsOptional()
    @IsString()
    organizationWebsite?: string;

    @ApiProperty({ description: 'Home country representative name' })
    @IsNotEmpty()
    @IsString()
    homeCountryRepresentative: string;

    @ApiProperty({ description: 'Rwanda representative name' })
    @IsNotEmpty()
    @IsString()
    rwandaRepresentative: string;

    @ApiProperty({ description: 'Organization RGB number' })
    @IsNotEmpty()
    @IsString()
    organizationRgbNumber: string;

    @ApiProperty({ description: 'Organization type ID' })
    @IsNotEmpty()
    @IsInt()
    organizationTypeId: number;

    @ApiProperty({
        type: [CreateAddressDto],
        description: 'Organization addresses (max 2: 1 headquarters, 1 Rwanda). At least one address is required.',
        maxItems: 2
    })
    @IsArray()
    @ArrayMaxSize(2)
    @ValidateNested({ each: true })
    @Type(() => CreateAddressDto)
    addresses: CreateAddressDto[];
}

export class UpdateOrganizationDto {
    @ApiProperty({ required: false, description: 'Organization name' })
    @IsOptional()
    @IsString()
    organizationName?: string;

    @ApiProperty({ required: false, description: 'Organization business registration number' })
    @IsOptional()
    @IsString()
    organizationRegistrationNumber?: string;

    @ApiProperty({ required: false, description: 'Organization phone number' })
    @IsOptional()
    @IsString()
    organizationPhoneNumber?: string;

    @ApiProperty({ required: false, description: 'Organization email address' })
    @IsOptional()
    @IsEmail()
    organizationEmail?: string;

    @ApiProperty({ required: false, description: 'Organization website URL' })
    @IsOptional()
    @IsString()
    organizationWebsite?: string;

    @ApiProperty({ required: false, description: 'Home country representative name' })
    @IsOptional()
    @IsString()
    homeCountryRepresentative?: string;

    @ApiProperty({ required: false, description: 'Rwanda representative name' })
    @IsOptional()
    @IsString()
    rwandaRepresentative?: string;

    @ApiProperty({ required: false, description: 'Organization RGB number' })
    @IsOptional()
    @IsString()
    organizationRgbNumber?: string;

    @ApiProperty({ required: false, description: 'Organization type ID' })
    @IsOptional()
    @IsInt()
    organizationTypeId?: number;
}

export class OrganizationResponseDto {
    @ApiProperty()
    id: string;

    @ApiProperty()
    organizationName: string;

    @ApiProperty()
    organizationRegistrationNumber: string;

    @ApiProperty()
    organizationPhoneNumber: string;

    @ApiProperty()
    organizationEmail: string;

    @ApiProperty({ required: false })
    organizationWebsite?: string;

    @ApiProperty()
    homeCountryRepresentative: string;

    @ApiProperty()
    rwandaRepresentative: string;

    @ApiProperty()
    organizationRgbNumber: string;

    @ApiProperty()
    organizationTypeId: number;

    @ApiProperty({ type: [AddressResponseDto] })
    addresses: AddressResponseDto[];

    @ApiProperty()
    createAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}

// DTO for PARTNER user registration with organization
export class CreateOrganizationWithUserDto {
    @ApiProperty({ description: 'User first name' })
    @IsNotEmpty()
    @IsString()
    firstName: string;

    @ApiProperty({ description: 'User last name' })
    @IsNotEmpty()
    @IsString()
    lastName: string;

    @ApiProperty({ description: 'User email address' })
    @IsNotEmpty()
    @IsEmail()
    email: string;

    @ApiProperty({ description: 'User password' })
    @IsNotEmpty()
    @IsString()
    password: string;

    @ApiProperty({ description: 'Organization details' })
    @ValidateNested()
    @Type(() => CreateOrganizationDto)
    organization: CreateOrganizationDto;
}
