export declare enum AddressType {
    HEADQUARTERS = "HEADQUARTERS",
    RWANDA = "RWANDA"
}
export declare class CreateAddressDto {
    addressType: AddressType;
    country: string;
    province?: string;
    district?: string;
    sector?: string;
    cell?: string;
    village?: string;
    street: string;
    avenue?: string;
    poBox: string;
    postalCode?: string;
    organizationId: string;
}
export declare class UpdateAddressDto {
    addressType?: AddressType;
    country?: string;
    province?: string;
    district?: string;
    sector?: string;
    cell?: string;
    village?: string;
    street?: string;
    avenue?: string;
    poBox?: string;
    postalCode?: string;
}
export declare class AddressResponseDto {
    id: string;
    addressType: AddressType;
    country: string;
    province?: string;
    district?: string;
    sector?: string;
    cell?: string;
    village?: string;
    street: string;
    avenue?: string;
    poBox: string;
    postalCode?: string;
    organizationId: string;
    createdAt: Date;
    updatedAt: Date;
    deleted: boolean;
}
