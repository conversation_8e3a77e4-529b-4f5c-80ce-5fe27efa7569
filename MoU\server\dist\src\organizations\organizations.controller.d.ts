import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
export declare class OrganizationsController {
    private readonly organizationsService;
    constructor(organizationsService: OrganizationsService);
    findAll(req: any): Promise<({
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    })[]>;
    findOne(id: string, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    create(createOrganizationDto: CreateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, req: any): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
