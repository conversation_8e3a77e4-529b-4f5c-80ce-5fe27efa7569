"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProjectActivityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CreateProjectActivityDto {
}
exports.CreateProjectActivityDto = CreateProjectActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique activity identifier',
        example: 'ACT-2024-001'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateProjectActivityDto.prototype, "activityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity name',
        example: 'Community Health Training'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateProjectActivityDto.prototype, "activityName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity description',
        required: false,
        example: 'Training local health workers in basic medical procedures'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectActivityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activity timeline',
        required: false,
        example: 'Q1 2024 - Q2 2024'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectActivityDto.prototype, "timeline", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget allocation for this activity',
        required: false,
        example: 50000.00
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    __metadata("design:type", Number)
], CreateProjectActivityDto.prototype, "budgetAllocation", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        required: false,
        default: 'USD',
        example: 'USD'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectActivityDto.prototype, "currency", void 0);
//# sourceMappingURL=create-project-activity.dto.js.map