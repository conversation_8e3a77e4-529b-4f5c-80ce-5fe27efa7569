"use client"

import { useState, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Upload, 
  File, 
  Trash2, 
  Eye, 
  Download, 
  CheckCircle,
  AlertTriangle,
  Info,
  FileText,
  Image,
  FileCheck
} from "lucide-react"
import { Step4Data, MouApplication, ApplicationDocument } from "@/lib/services/mou-application.service"

interface Step4DocumentUploadProps {
  data: Step4Data
  onChange: (data: Step4Data) => void
  application: MouApplication | null
}

interface UploadedDocument extends ApplicationDocument {
  uploadProgress?: number
  isUploading?: boolean
  error?: string
}

const DOCUMENT_TYPES = [
  { value: "organization_certificate", label: "Organization Certificate", required: true },
  { value: "tax_clearance", label: "Tax Clearance Certificate", required: true },
  { value: "financial_statements", label: "Financial Statements", required: false },
  { value: "project_proposal", label: "Project Proposal", required: false },
  { value: "technical_specifications", label: "Technical Specifications", required: false },
  { value: "budget_breakdown", label: "Budget Breakdown", required: false },
  { value: "partnership_agreement", label: "Partnership Agreement", required: false },
  { value: "other", label: "Other Supporting Documents", required: false }
]

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const ALLOWED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'image/jpeg',
  'image/png',
  'image/gif'
]

export function Step4DocumentUpload({ data, onChange, application }: Step4DocumentUploadProps) {
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([])
  const [dragOver, setDragOver] = useState(false)
  const [selectedDocumentType, setSelectedDocumentType] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return
    if (!selectedDocumentType) {
      alert("Please select a document type first")
      return
    }

    Array.from(files).forEach(file => {
      if (!validateFile(file)) return
      uploadFile(file, selectedDocumentType)
    })
  }

  const validateFile = (file: File): boolean => {
    if (file.size > MAX_FILE_SIZE) {
      alert(`File ${file.name} is too large. Maximum size is 10MB.`)
      return false
    }

    if (!ALLOWED_TYPES.includes(file.type)) {
      alert(`File ${file.name} has an unsupported format. Please use PDF, DOC, DOCX, or image files.`)
      return false
    }

    return true
  }

  const uploadFile = async (file: File, documentType: string) => {
    if (!application) {
      alert("Please save the application first before uploading documents")
      return
    }

    const documentTypeInfo = DOCUMENT_TYPES.find(dt => dt.value === documentType)
    const tempDoc: UploadedDocument = {
      id: `temp-${Date.now()}`,
      documentId: `temp-${Date.now()}`,
      fileName: file.name,
      originalName: file.name,
      mimeType: file.type,
      fileSize: file.size,
      filePath: "",
      documentType,
      isRequired: documentTypeInfo?.required || false,
      mouApplicationId: application.id,
      uploadedAt: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      deleted: false,
      uploadProgress: 0,
      isUploading: true
    }

    setUploadedDocuments(prev => [...prev, tempDoc])

    try {
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100))
        setUploadedDocuments(prev => 
          prev.map(doc => 
            doc.id === tempDoc.id 
              ? { ...doc, uploadProgress: progress }
              : doc
          )
        )
      }

      // In real implementation, this would call the API
      // const uploadedDoc = await mouApplicationService.uploadDocument(
      //   application.id, 
      //   file, 
      //   documentType, 
      //   documentTypeInfo?.required || false
      // )

      // Simulate successful upload
      const uploadedDoc: UploadedDocument = {
        ...tempDoc,
        id: `doc-${Date.now()}`,
        documentId: `doc-${Date.now()}`,
        filePath: `/uploads/${file.name}`,
        isUploading: false,
        uploadProgress: 100
      }

      setUploadedDocuments(prev => 
        prev.map(doc => 
          doc.id === tempDoc.id ? uploadedDoc : doc
        )
      )

    } catch (error) {
      setUploadedDocuments(prev => 
        prev.map(doc => 
          doc.id === tempDoc.id 
            ? { ...doc, isUploading: false, error: "Upload failed" }
            : doc
        )
      )
    }
  }

  const removeDocument = async (documentId: string) => {
    try {
      if (application) {
        // await mouApplicationService.deleteDocument(application.id, documentId)
      }
      setUploadedDocuments(prev => prev.filter(doc => doc.id !== documentId))
    } catch (error) {
      console.error("Failed to delete document:", error)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-4 w-4" />
    if (mimeType === 'application/pdf') return <FileText className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  const getRequiredDocuments = () => {
    return DOCUMENT_TYPES.filter(dt => dt.required)
  }

  const getUploadedRequiredDocuments = () => {
    const requiredTypes = getRequiredDocuments().map(dt => dt.value)
    return uploadedDocuments.filter(doc => 
      requiredTypes.includes(doc.documentType) && !doc.isUploading && !doc.error
    )
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const isStepComplete = () => {
    const requiredDocs = getRequiredDocuments()
    const uploadedRequiredDocs = getUploadedRequiredDocuments()
    return uploadedRequiredDocs.length >= requiredDocs.length
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">Document Upload</h3>
        <p className="text-muted-foreground">
          Upload supporting documents for your MoU application. Required documents must be uploaded to proceed.
        </p>
      </div>

      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-cyan-600" />
            Upload Documents
          </CardTitle>
          <CardDescription>
            Drag and drop files or click to browse. Maximum file size: 10MB
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="documentType">Document Type *</Label>
            <Select value={selectedDocumentType} onValueChange={setSelectedDocumentType}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {DOCUMENT_TYPES.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center gap-2">
                      <span>{type.label}</span>
                      {type.required && (
                        <Badge variant="destructive" className="text-xs">Required</Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver 
                ? 'border-cyan-400 bg-cyan-50' 
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-lg font-medium mb-2">Drop files here or click to browse</p>
            <p className="text-sm text-muted-foreground mb-4">
              Supported formats: PDF, DOC, DOCX, JPG, PNG, GIF
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              disabled={!selectedDocumentType}
              className="bg-cyan-600 hover:bg-cyan-700"
            >
              <Upload className="h-4 w-4 mr-2" />
              Choose Files
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>

      {/* Required Documents Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileCheck className="h-5 w-5 text-cyan-600" />
            Required Documents
          </CardTitle>
          <CardDescription>
            These documents are mandatory for your application
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {getRequiredDocuments().map((docType) => {
              const isUploaded = uploadedDocuments.some(doc => 
                doc.documentType === docType.value && !doc.isUploading && !doc.error
              )
              
              return (
                <div key={docType.value} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {isUploaded ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
                    )}
                    <div>
                      <p className="font-medium">{docType.label}</p>
                      <p className="text-sm text-muted-foreground">Required document</p>
                    </div>
                  </div>
                  <Badge variant={isUploaded ? "default" : "secondary"} className={isUploaded ? "bg-green-100 text-green-800" : ""}>
                    {isUploaded ? "Uploaded" : "Pending"}
                  </Badge>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Documents */}
      {uploadedDocuments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <File className="h-5 w-5 text-cyan-600" />
              Uploaded Documents ({uploadedDocuments.length})
            </CardTitle>
            <CardDescription>
              Manage your uploaded documents
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {uploadedDocuments.map((doc) => (
                <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3 flex-1">
                    {getFileIcon(doc.mimeType)}
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{doc.originalName}</p>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{formatFileSize(doc.fileSize)}</span>
                        <span>•</span>
                        <span>{DOCUMENT_TYPES.find(dt => dt.value === doc.documentType)?.label}</span>
                        {doc.isRequired && (
                          <>
                            <span>•</span>
                            <Badge variant="destructive" className="text-xs">Required</Badge>
                          </>
                        )}
                      </div>
                      {doc.isUploading && doc.uploadProgress !== undefined && (
                        <Progress value={doc.uploadProgress} className="w-full mt-2" />
                      )}
                      {doc.error && (
                        <p className="text-sm text-red-600 mt-1">{doc.error}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    {!doc.isUploading && !doc.error && (
                      <>
                        <Button size="sm" variant="ghost">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Download className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeDocument(doc.id)}
                      className="text-red-600 hover:text-red-700"
                      disabled={doc.isUploading}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Guidelines */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Document Guidelines</h4>
              <ul className="text-sm text-blue-800 mt-2 space-y-1">
                <li>• All documents must be clear and legible</li>
                <li>• Required documents are mandatory for application approval</li>
                <li>• File size limit: 10MB per document</li>
                <li>• Accepted formats: PDF, DOC, DOCX, JPG, PNG, GIF</li>
                <li>• Ensure all information is current and valid</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 4 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${getUploadedRequiredDocuments().length >= getRequiredDocuments().length ? 'text-green-700' : ''}`}>
                  {getUploadedRequiredDocuments().length >= getRequiredDocuments().length ? '✓' : '○'} 
                  All required documents uploaded ({getUploadedRequiredDocuments().length}/{getRequiredDocuments().length})
                </li>
                <li className={`flex items-center gap-2 ${uploadedDocuments.every(doc => !doc.isUploading && !doc.error) ? 'text-green-700' : ''}`}>
                  {uploadedDocuments.every(doc => !doc.isUploading && !doc.error) ? '✓' : '○'} 
                  All uploads completed successfully
                </li>
              </ul>
              {isStepComplete() && (
                <Alert className="mt-3 bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    All required documents have been uploaded. You can proceed to the final review.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
