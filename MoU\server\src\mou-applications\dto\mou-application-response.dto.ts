import { ApiProperty } from '@nestjs/swagger';
import { ApplicationStatus } from '@prisma/client';

export class MouApplicationResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the MoU application',
    example: 'uuid-application-id'
  })
  id: string;

  @ApiProperty({
    description: 'MoU application identifier',
    example: 'APP-2024-001'
  })
  mouApplicationId: string;

  @ApiProperty({
    description: 'MoU ID this application is for',
    example: 'uuid-mou-id'
  })
  mouId: string;

  @ApiProperty({
    description: 'Application responsibility description',
    required: false
  })
  responsibility?: string;

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus
  })
  status: ApplicationStatus;

  @ApiProperty({
    description: 'Current step in the application process (1-5)',
    minimum: 1,
    maximum: 5
  })
  currentStep: number;

  @ApiProperty({
    description: 'MoU duration in years',
    required: false
  })
  mouDurationYears?: number;

  @ApiProperty({
    description: 'Name of the signatory',
    required: false
  })
  signatoryName?: string;

  @ApiProperty({
    description: 'Position of the signatory',
    required: false
  })
  signatoryPosition?: string;

  @ApiProperty({
    description: 'Party name for this application',
    required: false
  })
  partyName?: string;

  @ApiProperty({
    description: 'Completion percentage (0-100)',
    minimum: 0,
    maximum: 100
  })
  completionPercentage: number;

  @ApiProperty({
    description: 'Last auto-save timestamp',
    required: false
  })
  lastAutoSave?: string;

  @ApiProperty({
    description: 'User ID who created this application',
    required: false
  })
  userId?: string;

  @ApiProperty({
    description: 'Creation timestamp'
  })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp'
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Soft delete flag'
  })
  deleted: boolean;

  @ApiProperty({
    description: 'Associated user information',
    required: false
  })
  user?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    organization?: {
      id: string;
      organizationName: string;
    };
  };

  @ApiProperty({
    description: 'Associated MoU information',
    required: false
  })
  mou?: {
    id: string;
    mouId: string;
    party: {
      id: string;
      name: string;
    };
  };

  @ApiProperty({
    description: 'Associated projects',
    required: false,
    isArray: true
  })
  projects?: any[];

  @ApiProperty({
    description: 'Application responsibilities',
    required: false,
    isArray: true
  })
  responsibilities?: any[];

  @ApiProperty({
    description: 'Application documents',
    required: false,
    isArray: true
  })
  documents?: any[];

  @ApiProperty({
    description: 'Approval steps',
    required: false,
    isArray: true
  })
  approvalSteps?: any[];
}
