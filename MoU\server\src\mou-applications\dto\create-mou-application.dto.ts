import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsInt, IsEnum, Min, Max } from 'class-validator';
import { ApplicationStatus } from '@prisma/client';

export class CreateMouApplicationDto {
  @ApiProperty({
    description: 'Unique MoU application identifier',
    example: 'APP-2024-001'
  })
  @IsString()
  @IsNotEmpty()
  mouApplicationId: string;

  @ApiProperty({
    description: 'MoU ID this application is for',
    example: 'uuid-mou-id'
  })
  @IsUUID()
  @IsNotEmpty()
  mouId: string;

  @ApiProperty({
    description: 'Application responsibility description',
    required: false,
    example: 'Healthcare service delivery in rural areas'
  })
  @IsOptional()
  @IsString()
  responsibility?: string;

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus,
    default: ApplicationStatus.DRAFT,
    required: false
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiProperty({
    description: 'Current step in the application process (1-5)',
    minimum: 1,
    maximum: 5,
    default: 1,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  currentStep?: number;

  @ApiProperty({
    description: 'MoU duration in years',
    required: false,
    example: 3
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  mouDurationYears?: number;

  @ApiProperty({
    description: 'Name of the signatory',
    required: false,
    example: 'Dr. John Doe'
  })
  @IsOptional()
  @IsString()
  signatoryName?: string;

  @ApiProperty({
    description: 'Position of the signatory',
    required: false,
    example: 'Chief Executive Officer'
  })
  @IsOptional()
  @IsString()
  signatoryPosition?: string;

  @ApiProperty({
    description: 'Party name for this application',
    required: false,
    example: 'Healthcare Partners International'
  })
  @IsOptional()
  @IsString()
  partyName?: string;

  @ApiProperty({
    description: 'Completion percentage (0-100)',
    minimum: 0,
    maximum: 100,
    default: 0,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  completionPercentage?: number;
}
