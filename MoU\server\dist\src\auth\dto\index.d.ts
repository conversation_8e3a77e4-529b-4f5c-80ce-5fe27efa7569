export declare enum UserRole {
    ADMIN = "ADMIN",
    PARTNER = "PARTNER",
    COORDINATOR = "COORDINATOR",
    LEGAL = "LEGAL",
    TECHNICAL_EXPERT = "TECHNICAL_EXPERT",
    HOD = "HOD",
    PS = "PS",
    MINISTER = "MINISTER"
}
export declare enum AddressType {
    HEADQUARTERS = "HEADQUARTERS",
    RWANDA = "RWANDA"
}
export declare class CreateAddressForRegistrationDto {
    addressType: AddressType;
    country: string;
    province?: string;
    district?: string;
    sector?: string;
    cell?: string;
    village?: string;
    street: string;
    avenue?: string;
    poBox: string;
    postalCode?: string;
}
export declare class CreateOrganizationForRegistrationDto {
    organizationName: string;
    organizationRegistrationNumber: string;
    organizationPhoneNumber: string;
    organizationEmail: string;
    organizationWebsite?: string;
    homeCountryRepresentative: string;
    rwandaRepresentative: string;
    organizationRgbNumber: string;
    organizationTypeId: number;
    addresses: CreateAddressForRegistrationDto[];
}
export declare class RegisterDto {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    organization: CreateOrganizationForRegistrationDto;
}
export declare class CreateUserByAdminDto {
    firstName: string;
    lastName: string;
    email: string;
    role: UserRole;
    organizationId?: string;
}
export declare class LoginDto {
    email: string;
    password: string;
}
export declare class RefreshTokenDto {
    refreshToken: string;
}
export declare class ForgotPasswordDto {
    email: string;
}
export declare class ResetPasswordDto {
    token: string;
    password: string;
}
export declare class VerifyEmailDto {
    token: string;
}
export declare class InviteUserDto {
    email: string;
    role: UserRole;
    organizationId?: string;
}
export declare class AcceptInvitationDto {
    token: string;
    firstName: string;
    lastName: string;
    password: string;
}
