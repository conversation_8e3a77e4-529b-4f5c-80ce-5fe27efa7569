"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Trash2, 
  User, 
  Building, 
  FileText, 
  Info,
  Save,
  Check
} from "lucide-react"
import { Step2Data, MouApplication } from "@/lib/services/mou-application.service"

interface Step2PartyDetailsProps {
  data: Step2Data
  onChange: (data: Step2Data) => void
  application: MouApplication | null
}

export function Step2PartyDetails({ data, onChange, application }: Step2PartyDetailsProps) {
  const [savingResponsibility, setSavingResponsibility] = useState<number | null>(null)
  const [savedResponsibilities, setSavedResponsibilities] = useState<Set<number>>(new Set())

  const handlePartyNameChange = (value: string) => {
    onChange({
      ...data,
      partyName: value
    })
  }

  const handleSignatoryNameChange = (value: string) => {
    onChange({
      ...data,
      signatoryName: value
    })
  }

  const handleSignatoryPositionChange = (value: string) => {
    onChange({
      ...data,
      signatoryPosition: value
    })
  }

  const handleResponsibilityChange = (index: number, value: string) => {
    const newResponsibilities = [...data.responsibilities]
    newResponsibilities[index] = value
    onChange({
      ...data,
      responsibilities: newResponsibilities
    })
    
    // Remove from saved set when modified
    const newSaved = new Set(savedResponsibilities)
    newSaved.delete(index)
    setSavedResponsibilities(newSaved)
  }

  const addResponsibility = () => {
    onChange({
      ...data,
      responsibilities: [...data.responsibilities, ""]
    })
  }

  const removeResponsibility = (index: number) => {
    if (data.responsibilities.length > 1) {
      const newResponsibilities = data.responsibilities.filter((_, i) => i !== index)
      onChange({
        ...data,
        responsibilities: newResponsibilities
      })
      
      // Update saved set
      const newSaved = new Set<number>()
      savedResponsibilities.forEach(savedIndex => {
        if (savedIndex < index) {
          newSaved.add(savedIndex)
        } else if (savedIndex > index) {
          newSaved.add(savedIndex - 1)
        }
      })
      setSavedResponsibilities(newSaved)
    }
  }

  const saveResponsibility = async (index: number) => {
    const responsibility = data.responsibilities[index]
    if (!responsibility.trim()) return

    try {
      setSavingResponsibility(index)
      
      // Simulate API call - in real implementation, this would save to backend
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const newSaved = new Set(savedResponsibilities)
      newSaved.add(index)
      setSavedResponsibilities(newSaved)
    } catch (error) {
      console.error("Failed to save responsibility:", error)
    } finally {
      setSavingResponsibility(null)
    }
  }

  const isFormValid = () => {
    return (
      data.partyName.trim() &&
      data.signatoryName.trim() &&
      data.signatoryPosition.trim() &&
      data.responsibilities.some(r => r.trim())
    )
  }

  const getCompletedResponsibilities = () => {
    return data.responsibilities.filter(r => r.trim()).length
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">Party Details</h3>
        <p className="text-muted-foreground">
          Provide signatory information and define your organization's responsibilities under this MoU.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Party Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Building className="h-4 w-4 text-cyan-600" />
              Party Information
            </CardTitle>
            <CardDescription>
              Organization details for this MoU
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="partyName">Party Name *</Label>
              <Input
                id="partyName"
                value={data.partyName}
                onChange={(e) => handlePartyNameChange(e.target.value)}
                placeholder="Enter party name"
              />
              <p className="text-xs text-muted-foreground">
                This can be different from your organization name if needed
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Signatory Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <User className="h-4 w-4 text-cyan-600" />
              Signatory Information
            </CardTitle>
            <CardDescription>
              Person authorized to sign this MoU
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="signatoryName">Signatory Name *</Label>
              <Input
                id="signatoryName"
                value={data.signatoryName}
                onChange={(e) => handleSignatoryNameChange(e.target.value)}
                placeholder="Full name of signatory"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="signatoryPosition">Position/Title *</Label>
              <Input
                id="signatoryPosition"
                value={data.signatoryPosition}
                onChange={(e) => handleSignatoryPositionChange(e.target.value)}
                placeholder="e.g., Executive Director, CEO"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Responsibilities Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-cyan-600" />
                Organization Responsibilities
              </CardTitle>
              <CardDescription>
                Define your organization's specific responsibilities and commitments
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {getCompletedResponsibilities()} of {data.responsibilities.length} completed
              </Badge>
              <Button onClick={addResponsibility} size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                Add Responsibility
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {data.responsibilities.map((responsibility, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor={`responsibility-${index}`} className="font-medium">
                  Responsibility {index + 1}
                  {savedResponsibilities.has(index) && (
                    <Badge variant="outline" className="ml-2 text-green-600 border-green-200">
                      <Check className="h-3 w-3 mr-1" />
                      Saved
                    </Badge>
                  )}
                </Label>
                <div className="flex items-center gap-1">
                  <Button
                    onClick={() => saveResponsibility(index)}
                    size="sm"
                    variant="ghost"
                    disabled={!responsibility.trim() || savingResponsibility === index || savedResponsibilities.has(index)}
                  >
                    {savingResponsibility === index ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-cyan-600" />
                    ) : (
                      <Save className="h-3 w-3" />
                    )}
                  </Button>
                  {data.responsibilities.length > 1 && (
                    <Button
                      onClick={() => removeResponsibility(index)}
                      size="sm"
                      variant="ghost"
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>

              <Textarea
                id={`responsibility-${index}`}
                value={responsibility}
                onChange={(e) => handleResponsibilityChange(index, e.target.value)}
                placeholder="Describe a specific responsibility or commitment..."
                rows={3}
                className={savedResponsibilities.has(index) ? "bg-green-50 border-green-200" : ""}
              />

              <p className="text-xs text-muted-foreground">
                Be specific about what your organization will do, deliver, or maintain under this MoU.
                Click the save button to preserve this responsibility.
              </p>
            </div>
          ))}

          {data.responsibilities.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No responsibilities added yet</p>
              <p className="text-sm">Click "Add Responsibility" to get started</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Guidelines */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900">Responsibility Guidelines</h4>
              <ul className="text-sm text-blue-800 mt-2 space-y-1">
                <li>• Be specific and measurable in your commitments</li>
                <li>• Include timelines where applicable</li>
                <li>• Consider resource requirements and capacity</li>
                <li>• Align with your organization's mission and capabilities</li>
                <li>• Save each responsibility individually before proceeding</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 2 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${data.partyName.trim() ? 'text-green-700' : ''}`}>
                  {data.partyName.trim() ? '✓' : '○'} Party name provided
                </li>
                <li className={`flex items-center gap-2 ${data.signatoryName.trim() ? 'text-green-700' : ''}`}>
                  {data.signatoryName.trim() ? '✓' : '○'} Signatory name provided
                </li>
                <li className={`flex items-center gap-2 ${data.signatoryPosition.trim() ? 'text-green-700' : ''}`}>
                  {data.signatoryPosition.trim() ? '✓' : '○'} Signatory position provided
                </li>
                <li className={`flex items-center gap-2 ${getCompletedResponsibilities() > 0 ? 'text-green-700' : ''}`}>
                  {getCompletedResponsibilities() > 0 ? '✓' : '○'} At least one responsibility defined
                </li>
              </ul>
              {isFormValid() && (
                <Alert className="mt-3 bg-green-50 border-green-200">
                  <Check className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    All required information completed. You can proceed to the next step.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
