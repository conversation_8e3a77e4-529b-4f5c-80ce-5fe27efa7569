import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID } from 'class-validator';

export class CreateMouDto {
  @ApiProperty({
    description: 'Unique MoU identifier',
    example: 'MOU-2024-001'
  })
  @IsString()
  @IsNotEmpty()
  mouId: string;

  @ApiProperty({
    description: 'Party ID associated with this MoU',
    example: 'uuid-party-id'
  })
  @IsUUID()
  @IsNotEmpty()
  partyId: string;
}
