import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards, 
  Request, 
  ParseIntPipe,
  UseInterceptors,
  UploadedFile,
  BadRequestException
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiBearerAuth, 
  ApiOperation, 
  ApiResponse, 
  ApiParam, 
  ApiConsumes,
  ApiBody
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { MouApplicationsService } from './mou-applications.service';
import { 
  CreateMouApplicationDto, 
  UpdateMouApplicationDto, 
  MouApplicationResponseDto,
  UpdateStepDto,
  AutoSaveDto,
  CreateResponsibilityDto,
  UploadDocumentDto
} from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';

@ApiTags('mou-applications')
@Controller('mou-applications')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class MouApplicationsController {
  constructor(private readonly mouApplicationsService: MouApplicationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new MoU application' })
  @ApiResponse({ status: 201, description: 'Application created successfully', type: MouApplicationResponseDto })
  @ApiResponse({ status: 409, description: 'Application with ID already exists' })
  @ApiResponse({ status: 404, description: 'MoU not found' })
  async create(@Body() createMouApplicationDto: CreateMouApplicationDto, @Request() req: any) {
    return this.mouApplicationsService.create(createMouApplicationDto, req.user.sub);
  }

  @Post('draft')
  @ApiOperation({ summary: 'Create a draft MoU application with minimal data' })
  @ApiResponse({ status: 201, description: 'Draft application created successfully', type: MouApplicationResponseDto })
  @ApiResponse({ status: 404, description: 'MoU not found' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        mouId: { type: 'string', description: 'MoU ID' }
      },
      required: ['mouId']
    }
  })
  async createDraft(@Body('mouId') mouId: string, @Request() req: any) {
    return this.mouApplicationsService.createDraft(mouId, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get all MoU applications (Admin sees all, others see their own)' })
  @ApiResponse({ status: 200, description: 'Returns list of applications', type: [MouApplicationResponseDto] })
  async findAll(@Request() req: any) {
    return this.mouApplicationsService.findAll(req.user.sub);
  }

  @Get('my-applications')
  @ApiOperation({ summary: 'Get current user\'s MoU applications' })
  @ApiResponse({ status: 200, description: 'Returns user\'s applications', type: [MouApplicationResponseDto] })
  async findMyApplications(@Request() req: any) {
    return this.mouApplicationsService.findMyApplications(req.user.sub);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get MoU application by ID' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Returns application details', type: MouApplicationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.mouApplicationsService.findOne(id, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a MoU application' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Application updated successfully', type: MouApplicationResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  @ApiResponse({ status: 409, description: 'Application with ID already exists' })
  async update(@Param('id') id: string, @Body() updateMouApplicationDto: UpdateMouApplicationDto, @Request() req: any) {
    return this.mouApplicationsService.update(id, updateMouApplicationDto, req.user.sub);
  }

  @Patch(':id/step/:stepNumber')
  @ApiOperation({ summary: 'Update specific step data with validation' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiParam({ name: 'stepNumber', description: 'Step number (1-5)' })
  @ApiResponse({ status: 200, description: 'Step updated successfully', type: MouApplicationResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid step number' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async updateStep(
    @Param('id') id: string, 
    @Param('stepNumber', ParseIntPipe) stepNumber: number, 
    @Body() updateStepDto: UpdateStepDto, 
    @Request() req: any
  ) {
    return this.mouApplicationsService.updateStep(id, stepNumber, updateStepDto, req.user.sub);
  }

  @Post(':id/auto-save')
  @ApiOperation({ summary: 'Auto-save draft functionality for form progress' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Auto-save successful' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async autoSave(@Param('id') id: string, @Body() autoSaveDto: AutoSaveDto, @Request() req: any) {
    return this.mouApplicationsService.autoSave(id, autoSaveDto, req.user.sub);
  }

  @Post(':id/responsibilities')
  @ApiOperation({ summary: 'Create and manage MoU responsibilities' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({ status: 201, description: 'Responsibility created successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async createResponsibility(
    @Param('id') id: string, 
    @Body() createResponsibilityDto: CreateResponsibilityDto, 
    @Request() req: any
  ) {
    return this.mouApplicationsService.createResponsibility(id, createResponsibilityDto, req.user.sub);
  }

  @Patch(':id/responsibilities/:responsibilityId')
  @ApiOperation({ summary: 'Update a responsibility' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiParam({ name: 'responsibilityId', description: 'Responsibility ID' })
  @ApiResponse({ status: 200, description: 'Responsibility updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Responsibility not found' })
  async updateResponsibility(
    @Param('id') id: string,
    @Param('responsibilityId') responsibilityId: string,
    @Body() updateData: Partial<CreateResponsibilityDto>,
    @Request() req: any
  ) {
    return this.mouApplicationsService.updateResponsibility(id, responsibilityId, updateData, req.user.sub);
  }

  @Delete(':id/responsibilities/:responsibilityId')
  @ApiOperation({ summary: 'Delete a responsibility' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiParam({ name: 'responsibilityId', description: 'Responsibility ID' })
  @ApiResponse({ status: 200, description: 'Responsibility deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Responsibility not found' })
  async deleteResponsibility(
    @Param('id') id: string,
    @Param('responsibilityId') responsibilityId: string,
    @Request() req: any
  ) {
    return this.mouApplicationsService.deleteResponsibility(id, responsibilityId, req.user.sub);
  }

  @Post(':id/documents')
  @ApiOperation({ summary: 'Handle document file uploads with proper validation' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Document file to upload'
        },
        documentType: {
          type: 'string',
          description: 'Type/category of the document'
        },
        isRequired: {
          type: 'boolean',
          description: 'Whether this document is required'
        }
      },
      required: ['file', 'documentType']
    }
  })
  @ApiResponse({ status: 201, description: 'Document uploaded successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file type or no file provided' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadDocument(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto,
    @Request() req: any
  ) {
    if (!file) {
      throw new BadRequestException('No file provided');
    }
    return this.mouApplicationsService.uploadDocument(id, file, uploadDocumentDto, req.user.sub);
  }

  @Delete(':id/documents/:documentId')
  @ApiOperation({ summary: 'Delete a document' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiParam({ name: 'documentId', description: 'Document ID' })
  @ApiResponse({ status: 200, description: 'Document deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async deleteDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
    @Request() req: any
  ) {
    return this.mouApplicationsService.deleteDocument(id, documentId, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a MoU application (only drafts)' })
  @ApiParam({ name: 'id', description: 'Application ID' })
  @ApiResponse({ status: 200, description: 'Application deleted successfully' })
  @ApiResponse({ status: 400, description: 'Only draft applications can be deleted' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Application not found' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.mouApplicationsService.remove(id, req.user.sub);
  }
}
