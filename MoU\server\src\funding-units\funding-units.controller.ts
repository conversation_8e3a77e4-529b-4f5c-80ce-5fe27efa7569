import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { FundingUnitsService } from './funding-units.service';
import { CreateFundingUnitDto, UpdateFundingUnitDto, FundingUnitResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('funding-units')
@Controller('funding-units')
export class FundingUnitsController {
  constructor(private readonly fundingUnitsService: FundingUnitsService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new funding unit (Admin only)' })
  @ApiResponse({ status: 201, description: 'Funding unit created successfully', type: FundingUnitResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Funding unit with name already exists' })
  async create(@Body() createFundingUnitDto: CreateFundingUnitDto, @Request() req: any) {
    return this.fundingUnitsService.create(createFundingUnitDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all funding units (Public access)' })
  @ApiResponse({ status: 200, description: 'List of funding units', type: [FundingUnitResponseDto] })
  async findAll() {
    return this.fundingUnitsService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get funding unit by ID (Public access)' })
  @ApiResponse({ status: 200, description: 'Funding unit details', type: FundingUnitResponseDto })
  @ApiResponse({ status: 404, description: 'Funding unit not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.fundingUnitsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update funding unit (Admin only)' })
  @ApiResponse({ status: 200, description: 'Funding unit updated successfully', type: FundingUnitResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Funding unit not found' })
  @ApiResponse({ status: 409, description: 'Funding unit with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateFundingUnitDto: UpdateFundingUnitDto, @Request() req: any) {
    return this.fundingUnitsService.update(id, updateFundingUnitDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete funding unit (Admin only)' })
  @ApiResponse({ status: 200, description: 'Funding unit deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Funding unit not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete funding unit that is being used' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.fundingUnitsService.remove(id, req.user.sub);
  }
}
