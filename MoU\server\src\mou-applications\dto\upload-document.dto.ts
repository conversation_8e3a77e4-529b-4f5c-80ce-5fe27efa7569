import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsBoolean, IsOptional } from 'class-validator';

export class UploadDocumentDto {
  @ApiProperty({
    description: 'Type/category of the document',
    example: 'organization_certificate'
  })
  @IsString()
  @IsNotEmpty()
  documentType: string;

  @ApiProperty({
    description: 'Whether this document is required',
    default: false
  })
  @IsBoolean()
  @IsOptional()
  isRequired?: boolean;
}
