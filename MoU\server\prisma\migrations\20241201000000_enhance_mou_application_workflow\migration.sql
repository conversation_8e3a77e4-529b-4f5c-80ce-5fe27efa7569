-- Add new enums for application status
CREATE TYPE "ApplicationStatus" AS ENUM ('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED');

-- Add new fields to MouApplication table
ALTER TABLE "MouApplication" ADD COLUMN "status" "ApplicationStatus" NOT NULL DEFAULT 'DRAFT';
ALTER TABLE "MouApplication" ADD COLUMN "currentStep" INTEGER NOT NULL DEFAULT 1;
ALTER TABLE "MouApplication" ADD COLUMN "mouDurationYears" INTEGER;
ALTER TABLE "MouApplication" ADD COLUMN "signatoryName" TEXT;
ALTER TABLE "MouApplication" ADD COLUMN "signatoryPosition" TEXT;
ALTER TABLE "MouApplication" ADD COLUMN "partyName" TEXT;
ALTER TABLE "MouApplication" ADD COLUMN "completionPercentage" INTEGER NOT NULL DEFAULT 0;
ALTER TABLE "MouApplication" ADD COLUMN "lastAutoSave" TIMESTAMP(3);

-- Create Responsibility table for dynamic responsibilities
CREATE TABLE "ApplicationResponsibility" (
    "id" TEXT NOT NULL,
    "responsibilityText" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "mouApplicationId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ApplicationResponsibility_pkey" PRIMARY KEY ("id")
);

-- Create ApplicationDocument table for file uploads
CREATE TABLE "ApplicationDocument" (
    "id" TEXT NOT NULL,
    "documentId" TEXT NOT NULL,
    "fileName" TEXT NOT NULL,
    "originalName" TEXT NOT NULL,
    "mimeType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "filePath" TEXT NOT NULL,
    "documentType" TEXT NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "mouApplicationId" TEXT NOT NULL,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ApplicationDocument_pkey" PRIMARY KEY ("id")
);

-- Create ProjectActivity table for project activities
CREATE TABLE "ProjectActivity" (
    "id" TEXT NOT NULL,
    "activityId" TEXT NOT NULL,
    "activityName" TEXT NOT NULL,
    "description" TEXT,
    "timeline" TEXT,
    "budgetAllocation" DECIMAL(15,2),
    "currency" TEXT DEFAULT 'USD',
    "projectId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ProjectActivity_pkey" PRIMARY KEY ("id")
);

-- Add new fields to Project table
ALTER TABLE "Project" ADD COLUMN "startDate" TIMESTAMP(3);
ALTER TABLE "Project" ADD COLUMN "endDate" TIMESTAMP(3);
ALTER TABLE "Project" ADD COLUMN "totalBudget" DECIMAL(15,2);
ALTER TABLE "Project" ADD COLUMN "currency" TEXT DEFAULT 'USD';

-- Create unique constraints
ALTER TABLE "ApplicationResponsibility" ADD CONSTRAINT "ApplicationResponsibility_documentId_key" UNIQUE ("documentId");
ALTER TABLE "ApplicationDocument" ADD CONSTRAINT "ApplicationDocument_documentId_key" UNIQUE ("documentId");
ALTER TABLE "ProjectActivity" ADD CONSTRAINT "ProjectActivity_activityId_key" UNIQUE ("activityId");

-- Add foreign key constraints
ALTER TABLE "ApplicationResponsibility" ADD CONSTRAINT "ApplicationResponsibility_mouApplicationId_fkey" FOREIGN KEY ("mouApplicationId") REFERENCES "MouApplication"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ApplicationDocument" ADD CONSTRAINT "ApplicationDocument_mouApplicationId_fkey" FOREIGN KEY ("mouApplicationId") REFERENCES "MouApplication"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "ProjectActivity" ADD CONSTRAINT "ProjectActivity_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "Project"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for better performance
CREATE INDEX "ApplicationResponsibility_mouApplicationId_idx" ON "ApplicationResponsibility"("mouApplicationId");
CREATE INDEX "ApplicationResponsibility_order_idx" ON "ApplicationResponsibility"("order");
CREATE INDEX "ApplicationDocument_mouApplicationId_idx" ON "ApplicationDocument"("mouApplicationId");
CREATE INDEX "ApplicationDocument_documentType_idx" ON "ApplicationDocument"("documentType");
CREATE INDEX "ProjectActivity_projectId_idx" ON "ProjectActivity"("projectId");
CREATE INDEX "MouApplication_status_idx" ON "MouApplication"("status");
CREATE INDEX "MouApplication_currentStep_idx" ON "MouApplication"("currentStep");
CREATE INDEX "MouApplication_userId_status_idx" ON "MouApplication"("userId", "status");
