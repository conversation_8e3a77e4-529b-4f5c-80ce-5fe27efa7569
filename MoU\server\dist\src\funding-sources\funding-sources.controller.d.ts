import { FundingSourcesService } from './funding-sources.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto } from './dto';
export declare class FundingSourcesController {
    private readonly fundingSourcesService;
    constructor(fundingSourcesService: FundingSourcesService);
    create(createFundingSourceDto: CreateFundingSourceDto, req: any): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    findAll(): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    update(id: number, updateFundingSourceDto: UpdateFundingSourceDto, req: any): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
}
