"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/page",{

/***/ "(app-pages-browser)/./lib/services/mou-application.service.ts":
/*!*************************************************!*\
  !*** ./lib/services/mou-application.service.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mouApplicationService: () => (/* binding */ mouApplicationService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./lib/api.ts\");\n\nconst mouApplicationService = {\n    // Get all MoU applications for the current user (partner)\n    async getMyApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/my-applications\");\n        return response.data;\n    },\n    // Get all MoU applications (admin/reviewer access)\n    async getAllApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications\");\n        return response.data;\n    },\n    // Get a specific MoU application by ID\n    async getApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Create a new MoU application\n    async createApplication (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications\", data);\n        return response.data;\n    },\n    // Update an existing MoU application\n    async updateApplication (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/mou-applications/\".concat(id), data);\n        return response.data;\n    },\n    // Delete a MoU application\n    async deleteApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Submit application for review\n    async submitApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(id, \"/submit\"));\n        return response.data;\n    },\n    // Get available MoUs for application\n    async getAvailableMous () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mous/available\");\n        return response.data;\n    },\n    // Get application statistics for dashboard\n    async getApplicationStats () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/stats\");\n        return response.data;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/mou-application.service.ts\n"));

/***/ })

});