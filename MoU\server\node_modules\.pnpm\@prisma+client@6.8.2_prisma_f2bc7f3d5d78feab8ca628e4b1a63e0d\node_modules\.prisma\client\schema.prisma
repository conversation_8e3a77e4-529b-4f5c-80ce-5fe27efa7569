// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Organization {
  id                             String  @id @default(uuid()) // Primary key (UUID)
  organizationName               String
  organizationRegistrationNumber String  @unique // Business registration number (human-readable)
  organizationPhoneNumber        String
  organizationEmail              String  @unique
  organizationWebsite            String?
  homeCountryRepresentative      String
  rwandaRepresentative           String
  organizationRgbNumber          String
  organizationTypeId             Int

  // Relationships
  organizationType OrganizationType @relation(fields: [organizationTypeId], references: [id])
  addresses        Address[] // Organization can have 1-2 addresses (headquarters and/or Rwanda)
  parties          Party[]
  documents        Document[]
  projects         Project[]
  users            User[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model OrganizationType {
  id            Int            @id @default(autoincrement())
  typeName      String         @unique
  organizations Organization[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Party {
  id      String @id @default(uuid()) // Primary Key
  partyId String @unique
  name    String

  responsibilityId Int
  organizationId   String
  objectiveId      Int
  goalId           Int

  signatory                 String
  position                  String
  duration                  Int     @default(1)
  reasonForExtendedDuration String?

  organization   Organization   @relation(fields: [organizationId], references: [id])
  responsibility Responsibility @relation(fields: [responsibilityId], references: [id])
  objective      Objective      @relation(fields: [objectiveId], references: [id])
  goal           Goal           @relation(fields: [goalId], references: [id])

  mou Mou?

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Responsibility {
  id      Int     @id @default(autoincrement())
  name    String
  parties Party[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Objective {
  id      Int     @id @default(autoincrement())
  name    String
  parties Party[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Goal {
  id      Int     @id @default(autoincrement())
  name    String
  parties Party[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Document {
  id          String  @id @default(uuid()) // PK
  documentId  String  @unique
  name        String
  description String?

  organizationId String // FK
  documentTypeId Int // FK

  organization Organization @relation(fields: [organizationId], references: [id])
  documentType DocumentType @relation(fields: [documentTypeId], references: [id])

  project Project? @relation("ProjectDocument")

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model DocumentType {
  id        Int        @id @default(autoincrement())
  typeName  String
  documents Document[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Activity {
  id                     String   @id @default(uuid()) // PK
  activityId             String   @unique
  name                   String
  description            String?
  projectId              String // FK
  startDate              DateTime
  endDate                DateTime
  implementer            String
  implementerUnit        String
  fiscalYear             Int
  domainOfInterventionId Int // FK
  inputId                String // FK

  project              Project              @relation(fields: [projectId], references: [id])
  domainOfIntervention DomainOfIntervention @relation(fields: [domainOfInterventionId], references: [id])
  input                Input                @relation(fields: [inputId], references: [id])

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model DomainOfIntervention {
  id                        Int    @id @default(autoincrement()) // PK
  domainOfInterventionId    Int    @unique
  domainName                String
  subDomainOfInterventionId Int // FK

  subDomainOfIntervention SubDomainOfIntervention @relation(fields: [subDomainOfInterventionId], references: [id])
  activities              Activity[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model SubDomainOfIntervention {
  id                        Int    @id @default(autoincrement()) // PK
  subDomainOfInterventionId Int    @unique
  subDomainName             String
  domainFunctionId          Int // FK

  domainFunction DomainFunction         @relation(fields: [domainFunctionId], references: [id])
  domains        DomainOfIntervention[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model DomainFunction {
  id                  Int    @id @default(autoincrement()) // PK
  domainFunctionId    Int    @unique
  domainFunctionName  String
  subDomainFunctionId Int // FK

  subDomainFunction SubDomainFunction         @relation(fields: [subDomainFunctionId], references: [id])
  subDomains        SubDomainOfIntervention[]

  technicalExperts User[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model SubDomainFunction {
  id                  Int    @id @default(autoincrement()) // PK
  subDomainFunctionId Int    @unique
  domainFunctionName  String

  domainFunctions DomainFunction[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Input {
  id              String @id @default(uuid()) // PK
  inputId         String @unique
  name            String
  inputSubclassId Int // FK

  inputSubclass InputSubclass @relation(fields: [inputSubclassId], references: [id])
  activities    Activity[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model InputSubclass {
  id              Int    @id @default(autoincrement()) // PK
  inputSubclassId Int    @unique
  name            String
  budget          Float

  inputs Input[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Project {
  id          String  @id @default(uuid()) // PK
  projectId   String  @unique
  name        String
  description String?
  duration    Int

  // Enhanced fields for multi-step form
  startDate   DateTime?
  endDate     DateTime?
  totalBudget Decimal?  @db.Decimal(15, 2)
  currency    String?   @default("USD")

  budgetTypeId      Int // FK
  fundingUnitId     Int // FK
  fundingSourceId   Int // FK
  organizationId    String // FK
  projectDocumentId String @unique // FK
  mouApplicationId  String

  mouApplication  MouApplication @relation(fields: [mouApplicationId], references: [id])
  budgetType      BudgetType     @relation(fields: [budgetTypeId], references: [id])
  fundingUnit     FundingUnit    @relation(fields: [fundingUnitId], references: [id])
  fundingSource   FundingSource  @relation(fields: [fundingSourceId], references: [id])
  projectDocument Document       @relation("ProjectDocument", fields: [projectDocumentId], references: [id])
  organization    Organization   @relation(fields: [organizationId], references: [id])

  activities        Activity[]
  projectActivities ProjectActivity[]
  approvalSteps     ApprovalStep[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model BudgetType {
  id       Int       @id @default(autoincrement())
  typeName String
  projects Project[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model FundingUnit {
  id       Int       @id @default(autoincrement())
  unitName String
  projects Project[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model FundingSource {
  id         Int       @id @default(autoincrement())
  sourceName String
  projects   Project[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model HealthCareProvider {
  id           Int     @id @default(autoincrement())
  providerName String  @unique
  description  String?
  location     String?
  contactEmail String?
  contactPhone String?
  website      String?

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model FinancingAgent {
  id          Int     @id @default(autoincrement())
  agentName   String  @unique
  description String?
  contactInfo String?

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model FinancingScheme {
  id          Int     @id @default(autoincrement())
  schemeName  String  @unique
  description String?
  terms       String?
  conditions  String?

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model InputCategory {
  id           Int             @id @default(autoincrement())
  categoryName String          @unique
  description  String?
  parentId     Int? // Self-referencing for nested structure
  parent       InputCategory?  @relation("InputCategoryHierarchy", fields: [parentId], references: [id])
  children     InputCategory[] @relation("InputCategoryHierarchy")

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Currency {
  id           Int    @id @default(autoincrement())
  currencyCode String @unique
  currencyName String

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model DomainIntervention {
  id          Int                  @id @default(autoincrement())
  domainName  String
  description String?
  parentId    Int? // Self-referencing for nested structure
  parent      DomainIntervention?  @relation("DomainHierarchy", fields: [parentId], references: [id])
  children    DomainIntervention[] @relation("DomainHierarchy")

  //timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model Mou {
  id      String @id @default(uuid()) // PK
  mouId   String @unique
  partyId String @unique // FK

  party           Party            @relation(fields: [partyId], references: [id])
  mouApplications MouApplication[]

  //timestamps
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

enum UserRole {
  ADMIN
  PARTNER
  COORDINATOR
  LEGAL
  TECHNICAL_EXPERT
  HOD
  PS
  MINISTER
}

enum ApprovalStatusType {
  PENDING
  RECOMMEND_FOR_APPROVAL
  RECOMMEND_FOR_MODIFICATION
  REJECT
  APPROVE
}

enum ApplicationStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  APPROVED
  REJECTED
}

enum AddressType {
  HEADQUARTERS
  RWANDA
}

model Address {
  id          String      @id @default(uuid())
  addressType AddressType
  country     String
  province    String? // Optional for headquarters addresses
  district    String? // Optional for headquarters addresses
  sector      String? // Rwanda-specific administrative division
  cell        String? // Rwanda-specific administrative division
  village     String? // Rwanda-specific administrative division
  street      String
  avenue      String?
  poBox       String
  postalCode  String?

  // Organization relationship
  // Business rules:
  // - Organizations can have 1-2 addresses maximum
  // - Address types: HEADQUARTERS and/or RWANDA
  // - Each organization must have at least one address (either type)
  // - Each organization can have at most one address of each type
  // - Supports: Rwanda-only, Headquarters-only, or both address types
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)

  // Ensure unique address type per organization (max 1 headquarters, max 1 Rwanda)
  @@unique([organizationId, addressType])
}

model User {
  id            String    @id @default(uuid())
  firstName     String
  lastName      String
  email         String    @unique
  password      String
  emailVerified Boolean   @default(false)
  verifiedAt    DateTime?

  verificationToken           String?
  verificationTokenExpiryTime DateTime?
  refreshToken                String?

  // Password reset fields
  passwordResetToken   String?
  passwordResetExpires DateTime?

  // Invitation fields
  invitationToken   String?
  invitationExpires DateTime?
  invitedBy         String?

  role UserRole @default(PARTNER)

  //tecnicalExpert (not sure yet)
  assignedDomains DomainFunction[]

  // Organization relationship (optional)
  // Business rules:
  // - PARTNER users: Must be associated with an organization (self-register with org data)
  // - ADMIN users: Should NOT be associated with any organization
  // - Other roles: May or may not be associated with an organization
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id])

  // MOU Applications created by this user
  mouApplications MouApplication[]

  // Approval steps where this user is the reviewer
  approvalStepsGiven ApprovalStep[] @relation("ApprovalReviewer")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)
}

model ApprovalStep {
  id String @id @default(uuid())

  // MOU Application relationship
  mouApplicationId String
  mouApplication   MouApplication @relation(fields: [mouApplicationId], references: [id])

  // Reviewer relationship
  reviewerId String
  reviewer   User     @relation("ApprovalReviewer", fields: [reviewerId], references: [id])
  role       UserRole

  // Project relationship (optional, only for technical experts)
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id])

  status  ApprovalStatusType @default(PENDING)
  comment String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MouApplication {
  id               String  @id @default(uuid())
  mouApplicationId String  @unique
  mouId            String
  responsibility   String?

  // Enhanced workflow fields
  status               ApplicationStatus @default(DRAFT)
  currentStep          Int               @default(1)
  mouDurationYears     Int?
  signatoryName        String?
  signatoryPosition    String?
  partyName            String?
  completionPercentage Int               @default(0)
  lastAutoSave         DateTime?

  // MOU relationship
  mou Mou @relation(fields: [mouId], references: [id])

  projects         Project[]
  responsibilities ApplicationResponsibility[]
  documents        ApplicationDocument[]
  // Approval steps for this application
  approvalSteps    ApprovalStep[]

  // User who created this application
  userId String?
  user   User?   @relation(fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  deleted Boolean @default(false)

  @@index([status])
  @@index([currentStep])
  @@index([userId, status])
}

model ApplicationResponsibility {
  id                 String @id @default(uuid())
  responsibilityText String
  order              Int
  mouApplicationId   String

  mouApplication MouApplication @relation(fields: [mouApplicationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@index([mouApplicationId])
  @@index([order])
}

model ApplicationDocument {
  id               String   @id @default(uuid())
  documentId       String   @unique
  fileName         String
  originalName     String
  mimeType         String
  fileSize         Int
  filePath         String
  documentType     String
  isRequired       Boolean  @default(false)
  mouApplicationId String
  uploadedAt       DateTime @default(now())

  mouApplication MouApplication @relation(fields: [mouApplicationId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@index([mouApplicationId])
  @@index([documentType])
}

model ProjectActivity {
  id               String   @id @default(uuid())
  activityId       String   @unique
  activityName     String
  description      String?
  timeline         String?
  budgetAllocation Decimal? @db.Decimal(15, 2)
  currency         String?  @default("USD")
  projectId        String

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@index([projectId])
}
