import { BudgetTypesService } from './budget-types.service';
import { CreateBudgetTypeDto, UpdateBudgetTypeDto } from './dto';
export declare class BudgetTypesController {
    private readonly budgetTypesService;
    constructor(budgetTypesService: BudgetTypesService);
    create(createBudgetTypeDto: CreateBudgetTypeDto, req: any): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    findAll(): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateBudgetTypeDto: UpdateBudgetTypeDto, req: any): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
}
