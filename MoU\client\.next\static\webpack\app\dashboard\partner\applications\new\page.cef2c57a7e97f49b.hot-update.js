"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/step1-mou-information.tsx":
/*!***********************************************************!*\
  !*** ./components/wizard-steps/step1-mou-information.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Step1MouInformation: () => (/* binding */ Step1MouInformation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/mou-application.service */ \"(app-pages-browser)/./lib/services/mou-application.service.ts\");\n/* __next_internal_client_entry_do_not_use__ Step1MouInformation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Step1MouInformation(param) {\n    let { data, onChange, application } = param;\n    _s();\n    const [availableMous, setAvailableMous] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedMou, setSelectedMou] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Step1MouInformation.useEffect\": ()=>{\n            loadAvailableMous();\n        }\n    }[\"Step1MouInformation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Step1MouInformation.useEffect\": ()=>{\n            if (data.mouId && availableMous.length > 0) {\n                const mou = availableMous.find({\n                    \"Step1MouInformation.useEffect.mou\": (m)=>m.id === data.mouId\n                }[\"Step1MouInformation.useEffect.mou\"]);\n                setSelectedMou(mou || null);\n            }\n        }\n    }[\"Step1MouInformation.useEffect\"], [\n        data.mouId,\n        availableMous\n    ]);\n    const loadAvailableMous = async ()=>{\n        try {\n            setLoading(true);\n            const mous = await _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_7__.mouApplicationService.getAvailableMous();\n            setAvailableMous(mous);\n        } catch (err) {\n            setError(\"Failed to load available MoUs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleMouChange = (mouId)=>{\n        const mou = availableMous.find((m)=>m.id === mouId);\n        setSelectedMou(mou || null);\n        onChange({\n            ...data,\n            mouId\n        });\n    };\n    const handleDurationChange = (value)=>{\n        const years = parseInt(value);\n        if (years >= 1 && years <= 10) {\n            onChange({\n                ...data,\n                mouDurationYears: years\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-cyan-900 mb-2\",\n                        children: \"MoU Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Select the Memorandum of Understanding you want to apply for and set the duration.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2 text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyan-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Organization Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Your organization details (read-only)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            htmlFor: \"organizationName\",\n                                            children: \"Organization Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"organizationName\",\n                                            value: data.organizationName,\n                                            readOnly: true,\n                                            className: \"bg-gray-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"This information is automatically populated from your account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2 text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyan-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"MoU Duration\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Set the duration for this MoU agreement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            htmlFor: \"mouDurationYears\",\n                                            children: \"Duration (Years) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: data.mouDurationYears.toString(),\n                                            onValueChange: handleDurationChange,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                        placeholder: \"Select duration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    children: Array.from({\n                                                        length: 10\n                                                    }, (_, i)=>i + 1).map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: year.toString(),\n                                                            children: [\n                                                                year,\n                                                                \" \",\n                                                                year === 1 ? 'Year' : 'Years'\n                                                            ]\n                                                        }, year, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Duration must be between 1 and 10 years\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-cyan-50 border-cyan-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-cyan-600 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-cyan-900\",\n                                        children: \"Step 1 Requirements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-cyan-800 mt-2 space-y-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-center gap-2 \".concat(data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? 'text-green-700' : ''),\n                                            children: [\n                                                data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? '✓' : '○',\n                                                \" Set MoU duration (1-10 years)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Step1MouInformation, \"kxVYKBDRbuhbtnHa4LImdULfyiM=\");\n_c = Step1MouInformation;\nvar _c;\n$RefreshReg$(_c, \"Step1MouInformation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/step1-mou-information.tsx\n"));

/***/ })

});