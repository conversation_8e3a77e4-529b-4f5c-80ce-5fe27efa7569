import api from "../api"

export interface BudgetType {
  id: number
  typeName: string
  createAt: string
  updatedAt: string
}

export interface FundingSource {
  id: number
  sourceName: string
  createAt: string
  updatedAt: string
}

export interface FundingUnit {
  id: number
  unitName: string
  createAt: string
  updatedAt: string
}

export const financialService = {
  // Budget Types
  async getBudgetTypes(): Promise<BudgetType[]> {
    const response = await api.get("/budget-types")
    return response.data
  },

  async createBudgetType(typeName: string): Promise<BudgetType> {
    const response = await api.post("/budget-types", { typeName })
    return response.data
  },

  async updateBudgetType(id: number, typeName: string): Promise<BudgetType> {
    const response = await api.put(`/budget-types/${id}`, { typeName })
    return response.data
  },

  async deleteBudgetType(id: number): Promise<void> {
    await api.delete(`/budget-types/${id}`)
  },

  // Funding Sources
  async getFundingSources(): Promise<FundingSource[]> {
    const response = await api.get("/funding-sources")
    return response.data
  },

  async createFundingSource(sourceName: string): Promise<FundingSource> {
    const response = await api.post("/funding-sources", { sourceName })
    return response.data
  },

  async updateFundingSource(id: number, sourceName: string): Promise<FundingSource> {
    const response = await api.put(`/funding-sources/${id}`, { sourceName })
    return response.data
  },

  async deleteFundingSource(id: number): Promise<void> {
    await api.delete(`/funding-sources/${id}`)
  },

  // Funding Units
  async getFundingUnits(): Promise<FundingUnit[]> {
    const response = await api.get("/funding-units")
    return response.data
  },

  async createFundingUnit(unitName: string): Promise<FundingUnit> {
    const response = await api.post("/funding-units", { unitName })
    return response.data
  },

  async updateFundingUnit(id: number, unitName: string): Promise<FundingUnit> {
    const response = await api.put(`/funding-units/${id}`, { unitName })
    return response.data
  },

  async deleteFundingUnit(id: number): Promise<void> {
    await api.delete(`/funding-units/${id}`)
  },
}
