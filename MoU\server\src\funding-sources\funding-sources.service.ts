import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto } from './dto';

@Injectable()
export class FundingSourcesService {
    private readonly logger = new Logger(FundingSourcesService.name);

    constructor(private prisma: PrismaService) {}

    async findAll() {
        try {
            const fundingSources = await this.prisma.fundingSource.findMany({
                where: { deleted: false },
                orderBy: { createAt: 'desc' }
            });

            return fundingSources;
        } catch (error) {
            this.logger.error('Failed to fetch funding sources', error.stack);
            throw new Error('Failed to fetch funding sources');
        }
    }

    async findOne(id: number) {
        try {
            const fundingSource = await this.prisma.fundingSource.findUnique({
                where: { id, deleted: false }
            });

            if (!fundingSource) {
                throw new NotFoundException('Funding source not found');
            }

            return fundingSource;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch funding source with ID ${id}`, error.stack);
            throw new Error('Failed to fetch funding source');
        }
    }

    async create(createFundingSourceDto: CreateFundingSourceDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create funding sources
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create funding sources');
            }

            // Check if funding source with same name already exists
            const existingSource = await this.prisma.fundingSource.findFirst({
                where: { 
                    sourceName: createFundingSourceDto.sourceName,
                    deleted: false 
                }
            });

            if (existingSource) {
                throw new ConflictException('Funding source with this name already exists');
            }

            const fundingSource = await this.prisma.fundingSource.create({
                data: {
                    sourceName: createFundingSourceDto.sourceName
                }
            });

            return fundingSource;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create funding source', error.stack);
            throw new Error('Failed to create funding source');
        }
    }

    async update(id: number, updateFundingSourceDto: UpdateFundingSourceDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can update funding sources
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update funding sources');
            }

            // Check if funding source exists
            const existingSource = await this.prisma.fundingSource.findUnique({
                where: { id, deleted: false }
            });

            if (!existingSource) {
                throw new NotFoundException('Funding source not found');
            }

            // Check if new name conflicts with existing sources
            if (updateFundingSourceDto.sourceName && updateFundingSourceDto.sourceName !== existingSource.sourceName) {
                const nameConflict = await this.prisma.fundingSource.findFirst({
                    where: { 
                        sourceName: updateFundingSourceDto.sourceName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Funding source with this name already exists');
                }
            }

            const fundingSource = await this.prisma.fundingSource.update({
                where: { id },
                data: updateFundingSourceDto
            });

            return fundingSource;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update funding source with ID ${id}`, error.stack);
            throw new Error('Failed to update funding source');
        }
    }

    async remove(id: number, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete funding sources
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete funding sources');
            }

            // Check if funding source exists
            const existingSource = await this.prisma.fundingSource.findUnique({
                where: { id, deleted: false }
            });

            if (!existingSource) {
                throw new NotFoundException('Funding source not found');
            }

            // Check if funding source is being used by any projects
            const projectsUsingSource = await this.prisma.project.findFirst({
                where: { 
                    fundingSourceId: id,
                    deleted: false 
                }
            });

            if (projectsUsingSource) {
                throw new ConflictException('Cannot delete funding source that is being used by projects');
            }

            // Soft delete the funding source
            await this.prisma.fundingSource.update({
                where: { id },
                data: { deleted: true }
            });

            return { message: 'Funding source deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete funding source with ID ${id}`, error.stack);
            throw new Error('Failed to delete funding source');
        }
    }
}
