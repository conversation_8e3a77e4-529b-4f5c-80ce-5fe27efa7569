import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateProjectDto, UpdateProjectDto, CreateProjectActivityDto, UpdateProjectActivityDto } from './dto';

@Injectable()
export class ProjectsService {
  constructor(private prisma: PrismaService) {}

  async create(createProjectDto: CreateProjectDto, userId: string) {
    // Verify user exists and has access
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if project ID already exists
    const existingProject = await this.prisma.project.findUnique({
      where: { projectId: createProjectDto.projectId }
    });

    if (existingProject) {
      throw new ConflictException('Project with this ID already exists');
    }

    // Verify MoU application exists and user has access
    const mouApplication = await this.prisma.mouApplication.findUnique({
      where: { id: createProjectDto.mouApplicationId }
    });

    if (!mouApplication) {
      throw new NotFoundException('MoU application not found');
    }

    // Check access permissions
    if (user.role !== 'ADMIN' && mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Verify related entities exist
    const [budgetType, fundingUnit, fundingSource, organization, document] = await Promise.all([
      this.prisma.budgetType.findUnique({ where: { id: createProjectDto.budgetTypeId } }),
      this.prisma.fundingUnit.findUnique({ where: { id: createProjectDto.fundingUnitId } }),
      this.prisma.fundingSource.findUnique({ where: { id: createProjectDto.fundingSourceId } }),
      this.prisma.organization.findUnique({ where: { id: createProjectDto.organizationId } }),
      this.prisma.document.findUnique({ where: { id: createProjectDto.projectDocumentId } })
    ]);

    if (!budgetType) throw new NotFoundException('Budget type not found');
    if (!fundingUnit) throw new NotFoundException('Funding unit not found');
    if (!fundingSource) throw new NotFoundException('Funding source not found');
    if (!organization) throw new NotFoundException('Organization not found');
    if (!document) throw new NotFoundException('Document not found');

    return this.prisma.project.create({
      data: {
        ...createProjectDto,
        startDate: createProjectDto.startDate ? new Date(createProjectDto.startDate) : null,
        endDate: createProjectDto.endDate ? new Date(createProjectDto.endDate) : null,
      },
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: {
          select: {
            id: true,
            organizationName: true,
          }
        },
        projectActivities: true,
        activities: true,
      }
    });
  }

  async findAll(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Admin can see all projects, others see only their organization's projects
    const whereClause = user.role === 'ADMIN' 
      ? { deleted: false }
      : {
          deleted: false,
          organizationId: user.organizationId
        };

    return this.prisma.project.findMany({
      where: whereClause,
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: {
          select: {
            id: true,
            organizationName: true,
          }
        },
        projectActivities: true,
        activities: true,
      },
      orderBy: { createAt: 'desc' }
    });
  }

  async findOne(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const project = await this.prisma.project.findUnique({
      where: { id, deleted: false },
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: {
          select: {
            id: true,
            organizationName: true,
          }
        },
        projectActivities: {
          where: { deleted: false },
          orderBy: { createdAt: 'asc' }
        },
        activities: {
          where: { deleted: false },
          orderBy: { createAt: 'asc' }
        },
        mouApplication: {
          select: {
            id: true,
            mouApplicationId: true,
            userId: true,
            status: true,
          }
        }
      }
    });

    if (!project) {
      throw new NotFoundException('Project not found');
    }

    // Check access permissions
    if (user.role !== 'ADMIN' && 
        project.organizationId !== user.organizationId &&
        project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return project;
  }

  async update(id: string, updateProjectDto: UpdateProjectDto, userId: string) {
    const project = await this.findOne(id, userId);

    // Only admin or project owner can update
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check for project ID conflicts if updating
    if (updateProjectDto.projectId && updateProjectDto.projectId !== project.projectId) {
      const existingProject = await this.prisma.project.findUnique({
        where: { projectId: updateProjectDto.projectId }
      });

      if (existingProject) {
        throw new ConflictException('Project with this ID already exists');
      }
    }

    const updateData: any = { ...updateProjectDto };
    if (updateProjectDto.startDate) updateData.startDate = new Date(updateProjectDto.startDate);
    if (updateProjectDto.endDate) updateData.endDate = new Date(updateProjectDto.endDate);

    return this.prisma.project.update({
      where: { id },
      data: updateData,
      include: {
        budgetType: true,
        fundingUnit: true,
        fundingSource: true,
        organization: {
          select: {
            id: true,
            organizationName: true,
          }
        },
        projectActivities: true,
        activities: true,
      }
    });
  }

  async createActivity(projectId: string, createProjectActivityDto: CreateProjectActivityDto, userId: string) {
    const project = await this.findOne(projectId, userId);

    // Only admin or project owner can add activities
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check if activity ID already exists
    const existingActivity = await this.prisma.projectActivity.findUnique({
      where: { activityId: createProjectActivityDto.activityId }
    });

    if (existingActivity) {
      throw new ConflictException('Activity with this ID already exists');
    }

    return this.prisma.projectActivity.create({
      data: {
        ...createProjectActivityDto,
        projectId,
      }
    });
  }

  async findActivities(projectId: string, userId: string) {
    const project = await this.findOne(projectId, userId);

    return this.prisma.projectActivity.findMany({
      where: {
        projectId,
        deleted: false
      },
      orderBy: { createdAt: 'asc' }
    });
  }

  async findActivity(projectId: string, activityId: string, userId: string) {
    const project = await this.findOne(projectId, userId);

    const activity = await this.prisma.projectActivity.findFirst({
      where: {
        id: activityId,
        projectId,
        deleted: false
      }
    });

    if (!activity) {
      throw new NotFoundException('Activity not found');
    }

    return activity;
  }

  async updateActivity(projectId: string, activityId: string, updateProjectActivityDto: UpdateProjectActivityDto, userId: string) {
    const project = await this.findOne(projectId, userId);
    const activity = await this.findActivity(projectId, activityId, userId);

    // Only admin or project owner can update activities
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check for activity ID conflicts if updating
    if (updateProjectActivityDto.activityId && updateProjectActivityDto.activityId !== activity.activityId) {
      const existingActivity = await this.prisma.projectActivity.findUnique({
        where: { activityId: updateProjectActivityDto.activityId }
      });

      if (existingActivity) {
        throw new ConflictException('Activity with this ID already exists');
      }
    }

    return this.prisma.projectActivity.update({
      where: { id: activityId },
      data: updateProjectActivityDto
    });
  }

  async deleteActivity(projectId: string, activityId: string, userId: string) {
    const project = await this.findOne(projectId, userId);
    const activity = await this.findActivity(projectId, activityId, userId);

    // Only admin or project owner can delete activities
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Soft delete
    await this.prisma.projectActivity.update({
      where: { id: activityId },
      data: { deleted: true }
    });

    return { message: 'Activity deleted successfully' };
  }

  async remove(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const project = await this.findOne(id, userId);

    // Only admin or project owner can delete
    if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check if project can be deleted (only if MoU application is still draft)
    if (project.mouApplication && project.mouApplication.status !== 'DRAFT') {
      throw new BadRequestException('Cannot delete project from submitted application');
    }

    // Soft delete the project (cascade will handle related records)
    await this.prisma.project.update({
      where: { id },
      data: { deleted: true }
    });

    return { message: 'Project deleted successfully' };
  }
}
