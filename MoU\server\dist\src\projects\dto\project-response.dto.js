"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ProjectResponseDto {
}
exports.ProjectResponseDto = ProjectResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the project',
        example: 'uuid-project-id'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project identifier',
        example: 'PROJ-2024-001'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project name',
        example: 'Rural Healthcare Initiative'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project description',
        required: false
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project duration in months',
        example: 36
    }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project start date',
        required: false
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project end date',
        required: false
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total project budget',
        required: false
    }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "totalBudget", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Currency code',
        required: false
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Budget type ID',
        example: 1
    }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "budgetTypeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding unit ID',
        example: 1
    }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "fundingUnitId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Funding source ID',
        example: 1
    }),
    __metadata("design:type", Number)
], ProjectResponseDto.prototype, "fundingSourceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Organization ID',
        example: 'uuid-organization-id'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "organizationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project document ID',
        example: 'uuid-document-id'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "projectDocumentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU application ID',
        example: 'uuid-application-id'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "mouApplicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "createAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp'
    }),
    __metadata("design:type", String)
], ProjectResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Soft delete flag'
    }),
    __metadata("design:type", Boolean)
], ProjectResponseDto.prototype, "deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated budget type information',
        required: false
    }),
    __metadata("design:type", Object)
], ProjectResponseDto.prototype, "budgetType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated funding unit information',
        required: false
    }),
    __metadata("design:type", Object)
], ProjectResponseDto.prototype, "fundingUnit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated funding source information',
        required: false
    }),
    __metadata("design:type", Object)
], ProjectResponseDto.prototype, "fundingSource", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated organization information',
        required: false
    }),
    __metadata("design:type", Object)
], ProjectResponseDto.prototype, "organization", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project activities',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], ProjectResponseDto.prototype, "projectActivities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Activities (legacy)',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], ProjectResponseDto.prototype, "activities", void 0);
//# sourceMappingURL=project-response.dto.js.map