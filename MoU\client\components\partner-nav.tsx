"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { 
  FileText, 
  Plus, 
  Home,
  Building,
  User,
  Settings
} from "lucide-react"

const partnerNavItems = [
  {
    title: "Dashboard",
    href: "/dashboard/partner",
    icon: Home,
  },
  {
    title: "Applications",
    href: "/dashboard/partner/applications",
    icon: FileText,
  },
  {
    title: "New Application",
    href: "/dashboard/partner/applications/new",
    icon: Plus,
  },
  {
    title: "Organization",
    href: "/dashboard/partner/organization",
    icon: Building,
  },
  {
    title: "Profile",
    href: "/dashboard/profile",
    icon: User,
  },
]

export function PartnerNav() {
  const pathname = usePathname()

  return (
    <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
      {partnerNavItems.map((item) => {
        const Icon = item.icon
        const isActive = pathname === item.href || 
          (item.href !== "/dashboard/partner" && pathname.startsWith(item.href))
        
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary",
              isActive && "bg-muted text-primary"
            )}
          >
            <Icon className="h-4 w-4" />
            {item.title}
          </Link>
        )
      })}
    </nav>
  )
}
