"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OrganizationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const dto_1 = require("../addresses/dto");
let OrganizationsService = OrganizationsService_1 = class OrganizationsService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(OrganizationsService_1.name);
    }
    async findAll(currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const whereClause = currentUser.role === 'ADMIN'
                ? { deleted: false }
                : {
                    deleted: false,
                    id: currentUser.organizationId || 'no-org'
                };
            const organizations = await this.prisma.organization.findMany({
                where: whereClause,
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                },
                orderBy: { createAt: 'desc' }
            });
            return organizations;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error('Failed to fetch organizations', error.stack);
            throw new Error('Failed to fetch organizations');
        }
    }
    async findOne(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const organization = await this.prisma.organization.findFirst({
                where: {
                    id,
                    deleted: false
                },
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                }
            });
            if (!organization) {
                throw new common_1.NotFoundException('Organization not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== id) {
                throw new common_1.ForbiddenException('You can only view your own organization');
            }
            return organization;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to fetch organization ${id}`, error.stack);
            throw new Error('Failed to fetch organization');
        }
    }
    async create(createOrganizationDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can create organizations');
            }
            await this.validateOrganizationData(createOrganizationDto);
            this.validateAddresses(createOrganizationDto.addresses);
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    OR: [
                        { organizationRegistrationNumber: createOrganizationDto.organizationRegistrationNumber },
                        { organizationEmail: createOrganizationDto.organizationEmail }
                    ],
                    deleted: false
                }
            });
            if (existingOrg) {
                throw new common_1.ConflictException('Organization with this registration number or email already exists');
            }
            const organization = await this.prisma.$transaction(async (prisma) => {
                const newOrg = await prisma.organization.create({
                    data: {
                        organizationName: createOrganizationDto.organizationName,
                        organizationRegistrationNumber: createOrganizationDto.organizationRegistrationNumber,
                        organizationPhoneNumber: createOrganizationDto.organizationPhoneNumber,
                        organizationEmail: createOrganizationDto.organizationEmail,
                        organizationWebsite: createOrganizationDto.organizationWebsite,
                        homeCountryRepresentative: createOrganizationDto.homeCountryRepresentative,
                        rwandaRepresentative: createOrganizationDto.rwandaRepresentative,
                        organizationRgbNumber: createOrganizationDto.organizationRgbNumber,
                        organizationTypeId: createOrganizationDto.organizationTypeId
                    }
                });
                for (const addressData of createOrganizationDto.addresses) {
                    await prisma.address.create({
                        data: {
                            ...addressData,
                            organizationId: newOrg.id
                        }
                    });
                }
                return await prisma.organization.findUnique({
                    where: { id: newOrg.id },
                    include: {
                        addresses: {
                            where: { deleted: false },
                            orderBy: { addressType: 'asc' }
                        },
                        organizationType: true
                    }
                });
            });
            return organization;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create organization', error.stack);
            throw new Error('Failed to create organization');
        }
    }
    async update(id, updateOrganizationDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingOrg) {
                throw new common_1.NotFoundException('Organization not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== id) {
                throw new common_1.ForbiddenException('You can only update your own organization');
            }
            if (updateOrganizationDto.organizationRegistrationNumber || updateOrganizationDto.organizationEmail) {
                const conflictConditions = [];
                if (updateOrganizationDto.organizationRegistrationNumber) {
                    conflictConditions.push({ organizationRegistrationNumber: updateOrganizationDto.organizationRegistrationNumber });
                }
                if (updateOrganizationDto.organizationEmail) {
                    conflictConditions.push({ organizationEmail: updateOrganizationDto.organizationEmail });
                }
                const conflictingOrg = await this.prisma.organization.findFirst({
                    where: {
                        OR: conflictConditions,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (conflictingOrg) {
                    throw new common_1.ConflictException('Organization with this registration number or email already exists');
                }
            }
            if (updateOrganizationDto.organizationTypeId) {
                const orgType = await this.prisma.organizationType.findUnique({
                    where: { id: updateOrganizationDto.organizationTypeId }
                });
                if (!orgType) {
                    throw new common_1.NotFoundException('Organization type not found');
                }
            }
            const organization = await this.prisma.organization.update({
                where: { id },
                data: updateOrganizationDto,
                include: {
                    addresses: {
                        where: { deleted: false },
                        orderBy: { addressType: 'asc' }
                    },
                    organizationType: true
                }
            });
            return organization;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update organization ${id}`, error.stack);
            throw new Error('Failed to update organization');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            if (currentUser.role !== 'ADMIN') {
                throw new common_1.ForbiddenException('Only administrators can delete organizations');
            }
            const existingOrg = await this.prisma.organization.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingOrg) {
                throw new common_1.NotFoundException('Organization not found');
            }
            const activeUsers = await this.prisma.user.count({
                where: {
                    organizationId: id,
                    deleted: false
                }
            });
            if (activeUsers > 0) {
                throw new common_1.BadRequestException('Cannot delete organization with active users');
            }
            await this.prisma.$transaction(async (prisma) => {
                await prisma.address.updateMany({
                    where: { organizationId: id },
                    data: { deleted: true }
                });
                await prisma.organization.update({
                    where: { id },
                    data: { deleted: true }
                });
            });
            return { message: 'Organization deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to delete organization ${id}`, error.stack);
            throw new Error('Failed to delete organization');
        }
    }
    async validateOrganizationData(createOrganizationDto) {
        const orgType = await this.prisma.organizationType.findUnique({
            where: { id: createOrganizationDto.organizationTypeId }
        });
        if (!orgType) {
            throw new common_1.NotFoundException('Organization type not found');
        }
    }
    validateAddresses(addresses) {
        if (!addresses || addresses.length === 0) {
            throw new common_1.BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }
        if (addresses.length > 2) {
            throw new common_1.BadRequestException('Organization cannot have more than 2 addresses');
        }
        const addressTypes = addresses.map(addr => addr.addressType);
        const uniqueTypes = new Set(addressTypes);
        if (addressTypes.length !== uniqueTypes.size) {
            throw new common_1.BadRequestException('Cannot have duplicate address types');
        }
        const validAddressTypes = [dto_1.AddressType.RWANDA, dto_1.AddressType.HEADQUARTERS];
        for (const address of addresses) {
            if (!validAddressTypes.includes(address.addressType)) {
                throw new common_1.BadRequestException('Invalid address type. Must be either RWANDA or HEADQUARTERS');
            }
        }
        const rwandaAddress = addresses.find(addr => addr.addressType === dto_1.AddressType.RWANDA);
        if (rwandaAddress && (!rwandaAddress.province || !rwandaAddress.district)) {
            throw new common_1.BadRequestException('Rwanda address must include province and district');
        }
        const hasRwandaAddress = addresses.some(addr => addr.addressType === dto_1.AddressType.RWANDA);
        const hasHeadquartersAddress = addresses.some(addr => addr.addressType === dto_1.AddressType.HEADQUARTERS);
        if (!hasRwandaAddress && !hasHeadquartersAddress) {
            throw new common_1.BadRequestException('Organization must have at least one address (either Rwanda or headquarters)');
        }
    }
};
exports.OrganizationsService = OrganizationsService;
exports.OrganizationsService = OrganizationsService = OrganizationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], OrganizationsService);
//# sourceMappingURL=organizations.service.js.map