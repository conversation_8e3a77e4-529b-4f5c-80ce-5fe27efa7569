import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateBudgetTypeDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    typeName: string;
}

export class UpdateBudgetTypeDto {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    typeName?: string;
}

export class BudgetTypeResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty()
    typeName: string;

    @ApiProperty()
    createAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}
