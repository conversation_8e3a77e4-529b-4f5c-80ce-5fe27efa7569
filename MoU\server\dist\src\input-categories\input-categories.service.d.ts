import { PrismaService } from '../prisma/prisma.service';
import { CreateInputCategoryDto, UpdateInputCategoryDto } from './dto';
export declare class InputCategoriesService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createInputCategoryDto: CreateInputCategoryDto, userId: string): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: ({
            children: ({
                children: {
                    id: number;
                    updatedAt: Date;
                    deleted: boolean;
                    createdAt: Date;
                    description: string | null;
                    categoryName: string;
                    parentId: number | null;
                }[];
            } & {
                id: number;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                description: string | null;
                categoryName: string;
                parentId: number | null;
            })[];
        } & {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        })[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    })[]>;
    findTree(): Promise<({
        children: ({
            children: ({
                children: ({
                    children: {
                        id: number;
                        updatedAt: Date;
                        deleted: boolean;
                        createdAt: Date;
                        description: string | null;
                        categoryName: string;
                        parentId: number | null;
                    }[];
                } & {
                    id: number;
                    updatedAt: Date;
                    deleted: boolean;
                    createdAt: Date;
                    description: string | null;
                    categoryName: string;
                    parentId: number | null;
                })[];
            } & {
                id: number;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                description: string | null;
                categoryName: string;
                parentId: number | null;
            })[];
        } & {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        })[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    })[]>;
    findOne(id: number): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: ({
            children: {
                id: number;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                description: string | null;
                categoryName: string;
                parentId: number | null;
            }[];
        } & {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        })[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    update(id: number, updateInputCategoryDto: UpdateInputCategoryDto, userId: string): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            categoryName: string;
            parentId: number | null;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    remove(id: number, userId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        categoryName: string;
        parentId: number | null;
    }>;
    private checkCircularReference;
}
