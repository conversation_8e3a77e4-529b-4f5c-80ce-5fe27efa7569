import { ApplicationStatus } from '@prisma/client';
export declare class MouApplicationResponseDto {
    id: string;
    mouApplicationId: string;
    mouId: string;
    responsibility?: string;
    status: ApplicationStatus;
    currentStep: number;
    mouDurationYears?: number;
    signatoryName?: string;
    signatoryPosition?: string;
    partyName?: string;
    completionPercentage: number;
    lastAutoSave?: string;
    userId?: string;
    createdAt: string;
    updatedAt: string;
    deleted: boolean;
    user?: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
        organization?: {
            id: string;
            organizationName: string;
        };
    };
    mou?: {
        id: string;
        mouId: string;
        party: {
            id: string;
            name: string;
        };
    };
    projects?: any[];
    responsibilities?: any[];
    documents?: any[];
    approvalSteps?: any[];
}
