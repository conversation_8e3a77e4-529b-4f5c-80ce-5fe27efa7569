import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { BudgetTypesService } from './budget-types.service';
import { CreateBudgetTypeDto, UpdateBudgetTypeDto, BudgetTypeResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';
import { Public } from '../auth/decorator/public.decorator';

@ApiTags('budget-types')
@Controller('budget-types')
export class BudgetTypesController {
  constructor(private readonly budgetTypesService: BudgetTypesService) {}

  @Post()
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new budget type (Admin only)' })
  @ApiResponse({ status: 201, description: 'Budget type created successfully', type: BudgetTypeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'Budget type with name already exists' })
  async create(@Body() createBudgetTypeDto: CreateBudgetTypeDto, @Request() req: any) {
    return this.budgetTypesService.create(createBudgetTypeDto, req.user.sub);
  }

  @Get()
  @Public()
  @ApiOperation({ summary: 'Get all budget types (Public access)' })
  @ApiResponse({ status: 200, description: 'List of budget types', type: [BudgetTypeResponseDto] })
  async findAll() {
    return this.budgetTypesService.findAll();
  }

  @Get(':id')
  @Public()
  @ApiOperation({ summary: 'Get budget type by ID (Public access)' })
  @ApiResponse({ status: 200, description: 'Budget type details', type: BudgetTypeResponseDto })
  @ApiResponse({ status: 404, description: 'Budget type not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.budgetTypesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update budget type (Admin only)' })
  @ApiResponse({ status: 200, description: 'Budget type updated successfully', type: BudgetTypeResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Budget type not found' })
  @ApiResponse({ status: 409, description: 'Budget type with name already exists' })
  async update(@Param('id', ParseIntPipe) id: number, @Body() updateBudgetTypeDto: UpdateBudgetTypeDto, @Request() req: any) {
    return this.budgetTypesService.update(id, updateBudgetTypeDto, req.user.sub);
  }

  @Delete(':id')
  @UseGuards(JwtGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete budget type (Admin only)' })
  @ApiResponse({ status: 200, description: 'Budget type deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'Budget type not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete budget type that is being used' })
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req: any) {
    return this.budgetTypesService.remove(id, req.user.sub);
  }
}
