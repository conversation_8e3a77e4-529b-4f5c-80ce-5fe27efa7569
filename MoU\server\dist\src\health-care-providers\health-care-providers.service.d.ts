import { PrismaService } from '../prisma/prisma.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';
export declare class HealthCareProvidersService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createHealthCareProviderDto: CreateHealthCareProviderDto, userId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, userId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    remove(id: number, userId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
}
