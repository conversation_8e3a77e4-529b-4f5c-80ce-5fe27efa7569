import api from "../api"

export type ApplicationStatus = 'DRAFT' | 'SUBMITTED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED'

export interface MouApplication {
  id: string
  mouApplicationId: string
  mouId: string
  responsibility?: string

  // Enhanced workflow fields
  status: ApplicationStatus
  currentStep: number
  mouDurationYears?: number
  signatoryName?: string
  signatoryPosition?: string
  partyName?: string
  completionPercentage: number
  lastAutoSave?: string

  userId?: string
  user?: {
    id: string
    firstName: string
    lastName: string
    email: string
    organization?: {
      id: string
      organizationName: string
    }
  }
  mou?: {
    id: string
    mouId: string
    party: {
      id: string
      partyName: string
    }
  }
  projects?: Project[]
  responsibilities?: ApplicationResponsibility[]
  documents?: ApplicationDocument[]
  approvalSteps?: ApprovalStep[]
  createdAt: string
  updatedAt: string
  deleted: boolean
}

export interface Project {
  id: string
  projectId: string
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
  status?: string
  mouApplicationId: string
  activities?: ProjectActivity[]
  createdAt: string
  updatedAt: string
}

export interface ApplicationResponsibility {
  id: string
  responsibilityText: string
  order: number
  mouApplicationId: string
  createdAt: string
  updatedAt: string
  deleted: boolean
}

export interface ApplicationDocument {
  id: string
  documentId: string
  fileName: string
  originalName: string
  mimeType: string
  fileSize: number
  filePath: string
  documentType: string
  isRequired: boolean
  mouApplicationId: string
  uploadedAt: string
  createdAt: string
  updatedAt: string
  deleted: boolean
}

export interface ProjectActivity {
  id: string
  activityId: string
  activityName: string
  description?: string
  timeline?: string
  budgetAllocation?: number
  currency?: string
  projectId: string
  createdAt: string
  updatedAt: string
  deleted: boolean
}

export interface ApprovalStep {
  id: string
  stepNumber: number
  reviewerRole: string
  reviewerId?: string
  reviewer?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  status: 'PENDING' | 'RECOMMEND_FOR_APPROVAL' | 'RECOMMEND_FOR_MODIFICATION' | 'REJECT' | 'APPROVE'
  comments?: string
  reviewedAt?: string
  mouApplicationId: string
  createdAt: string
  updatedAt: string
}

// Multi-step form data interfaces
export interface Step1Data {
  mouDurationYears: number
  organizationName: string // read-only, from user's organization
}

export interface Step2Data {
  partyName: string
  signatoryName: string
  signatoryPosition: string
  responsibilities: string[]
}

export interface Step3Data {
  projects: ProjectFormData[]
}

export interface ProjectFormData {
  id?: string
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
  activities: ActivityFormData[]
}

export interface ActivityFormData {
  id?: string
  activityName: string
  description?: string
  timeline?: string
  budgetAllocation?: number
  currency?: string
}

export interface Step4Data {
  documents: DocumentUploadData[]
}

export interface DocumentUploadData {
  file: File
  documentType: string
  isRequired: boolean
}

export interface Step5Data {
  termsAccepted: boolean
  finalReview: boolean
}

// Legacy interfaces for backward compatibility
export interface CreateMouApplicationRequest {
  mouId: string
  responsibility?: string
  projects?: CreateProjectRequest[]

  // Enhanced fields
  status?: ApplicationStatus
  currentStep?: number
  mouDurationYears?: number
  signatoryName?: string
  signatoryPosition?: string
  partyName?: string
}

export interface CreateProjectRequest {
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
  activities?: CreateActivityRequest[]
}

export interface CreateActivityRequest {
  activityName: string
  description?: string
  timeline?: string
  budgetAllocation?: number
  currency?: string
}

export interface UpdateMouApplicationRequest {
  responsibility?: string
  projects?: UpdateProjectRequest[]

  // Enhanced fields
  status?: ApplicationStatus
  currentStep?: number
  mouDurationYears?: number
  signatoryName?: string
  signatoryPosition?: string
  partyName?: string
  completionPercentage?: number
}

export interface UpdateProjectRequest {
  id?: string
  projectName: string
  projectDescription?: string
  startDate?: string
  endDate?: string
  totalBudget?: number
  currency?: string
  activities?: UpdateActivityRequest[]
}

export interface UpdateActivityRequest {
  id?: string
  activityName: string
  description?: string
  timeline?: string
  budgetAllocation?: number
  currency?: string
}

// Step-specific update interfaces
export interface UpdateStepRequest {
  stepNumber: number
  data: any
  autoSave?: boolean
}

export const mouApplicationService = {
  // Get all MoU applications for the current user (partner)
  async getMyApplications(): Promise<MouApplication[]> {
    const response = await api.get("/mou-applications/my-applications")
    return response.data
  },

  // Get all MoU applications (admin/reviewer access)
  async getAllApplications(): Promise<MouApplication[]> {
    const response = await api.get("/mou-applications")
    return response.data
  },

  // Get a specific MoU application by ID
  async getApplication(id: string): Promise<MouApplication> {
    const response = await api.get(`/mou-applications/${id}`)
    return response.data
  },

  // Create a new MoU application (draft by default)
  async createApplication(data: CreateMouApplicationRequest): Promise<MouApplication> {
    const response = await api.post("/mou-applications", {
      ...data,
      status: data.status || 'DRAFT',
      currentStep: data.currentStep || 1
    })
    return response.data
  },

  // Create a draft application with minimal data
  async createDraft(mouId: string): Promise<MouApplication> {
    const response = await api.post("/mou-applications/draft", {
      mouId,
      status: 'DRAFT',
      currentStep: 1,
      completionPercentage: 0
    })
    return response.data
  },

  // Update an existing MoU application
  async updateApplication(id: string, data: UpdateMouApplicationRequest): Promise<MouApplication> {
    const response = await api.put(`/mou-applications/${id}`, data)
    return response.data
  },

  // Update a specific step of the application
  async updateStep(id: string, stepNumber: number, data: any, autoSave = false): Promise<MouApplication> {
    const response = await api.patch(`/mou-applications/${id}/step/${stepNumber}`, {
      data,
      autoSave,
      lastAutoSave: autoSave ? new Date().toISOString() : undefined
    })
    return response.data
  },

  // Auto-save application data
  async autoSave(id: string, data: any): Promise<{ message: string }> {
    const response = await api.post(`/mou-applications/${id}/auto-save`, {
      ...data,
      lastAutoSave: new Date().toISOString()
    })
    return response.data
  },

  // Delete a MoU application
  async deleteApplication(id: string): Promise<{ message: string }> {
    const response = await api.delete(`/mou-applications/${id}`)
    return response.data
  },

  // Submit application for review (convert from DRAFT to SUBMITTED)
  async submitApplication(id: string): Promise<{ message: string }> {
    const response = await api.post(`/mou-applications/${id}/submit`)
    return response.data
  },

  // Get available MoUs for application
  async getAvailableMous(): Promise<any[]> {
    const response = await api.get("/mous/available")
    return response.data
  },

  // Get application statistics for dashboard
  async getApplicationStats(): Promise<{
    total: number
    pending: number
    approved: number
    rejected: number
    draft: number
  }> {
    const response = await api.get("/mou-applications/stats")
    return response.data
  },

  // Document upload methods
  async uploadDocument(applicationId: string, file: File, documentType: string, isRequired = false): Promise<ApplicationDocument> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('documentType', documentType)
    formData.append('isRequired', isRequired.toString())

    const response = await api.post(`/mou-applications/${applicationId}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  async deleteDocument(applicationId: string, documentId: string): Promise<{ message: string }> {
    const response = await api.delete(`/mou-applications/${applicationId}/documents/${documentId}`)
    return response.data
  },

  // Responsibility management
  async addResponsibility(applicationId: string, responsibilityText: string, order: number): Promise<ApplicationResponsibility> {
    const response = await api.post(`/mou-applications/${applicationId}/responsibilities`, {
      responsibilityText,
      order
    })
    return response.data
  },

  async updateResponsibility(applicationId: string, responsibilityId: string, responsibilityText: string): Promise<ApplicationResponsibility> {
    const response = await api.put(`/mou-applications/${applicationId}/responsibilities/${responsibilityId}`, {
      responsibilityText
    })
    return response.data
  },

  async deleteResponsibility(applicationId: string, responsibilityId: string): Promise<{ message: string }> {
    const response = await api.delete(`/mou-applications/${applicationId}/responsibilities/${responsibilityId}`)
    return response.data
  },

  // Project activity management
  async addProjectActivity(projectId: string, activity: CreateActivityRequest): Promise<ProjectActivity> {
    const response = await api.post(`/projects/${projectId}/activities`, activity)
    return response.data
  },

  async updateProjectActivity(projectId: string, activityId: string, activity: UpdateActivityRequest): Promise<ProjectActivity> {
    const response = await api.put(`/projects/${projectId}/activities/${activityId}`, activity)
    return response.data
  },

  async deleteProjectActivity(projectId: string, activityId: string): Promise<{ message: string }> {
    const response = await api.delete(`/projects/${projectId}/activities/${activityId}`)
    return response.data
  },

  // Validation methods
  async validateStep(applicationId: string, stepNumber: number): Promise<{ isValid: boolean; errors: string[] }> {
    const response = await api.post(`/mou-applications/${applicationId}/validate-step/${stepNumber}`)
    return response.data
  },

  // Progress calculation
  calculateCompletionPercentage(application: MouApplication): number {
    let completedSteps = 0
    const totalSteps = 5

    // Step 1: MoU Information
    if (application.mouDurationYears) {
      completedSteps++
    }

    // Step 2: Party Details
    if (application.partyName && application.signatoryName && application.signatoryPosition &&
        application.responsibilities && application.responsibilities.length > 0) {
      completedSteps++
    }

    // Step 3: Project Information
    if (application.projects && application.projects.length > 0) {
      const hasValidProjects = application.projects.some(p =>
        p.projectName && p.startDate && p.endDate && p.totalBudget
      )
      if (hasValidProjects) {
        completedSteps++
      }
    }

    // Step 4: Document Upload
    if (application.documents && application.documents.length > 0) {
      const hasRequiredDocs = application.documents.some(d => d.isRequired)
      if (hasRequiredDocs) {
        completedSteps++
      }
    }

    // Step 5: Review & Submit (completed when status is not DRAFT)
    if (application.status !== 'DRAFT') {
      completedSteps++
    }

    return Math.round((completedSteps / totalSteps) * 100)
  }
}
