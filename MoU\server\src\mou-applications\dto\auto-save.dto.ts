import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, IsEnum, Min, Max } from 'class-validator';
import { ApplicationStatus } from '@prisma/client';

export class AutoSaveDto {
  @ApiProperty({
    description: 'Current step being worked on (1-5)',
    minimum: 1,
    maximum: 5,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  currentStep?: number;

  @ApiProperty({
    description: 'Completion percentage (0-100)',
    minimum: 0,
    maximum: 100,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  completionPercentage?: number;

  @ApiProperty({
    description: 'Form data to save (JSON)',
    required: false
  })
  @IsOptional()
  formData?: any;

  @ApiProperty({
    description: 'Application responsibility description',
    required: false
  })
  @IsOptional()
  @IsString()
  responsibility?: string;

  @ApiProperty({
    description: 'MoU duration in years',
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  mouDurationYears?: number;

  @ApiProperty({
    description: 'Name of the signatory',
    required: false
  })
  @IsOptional()
  @IsString()
  signatoryName?: string;

  @ApiProperty({
    description: 'Position of the signatory',
    required: false
  })
  @IsOptional()
  @IsString()
  signatoryPosition?: string;

  @ApiProperty({
    description: 'Party name for this application',
    required: false
  })
  @IsOptional()
  @IsString()
  partyName?: string;
}
