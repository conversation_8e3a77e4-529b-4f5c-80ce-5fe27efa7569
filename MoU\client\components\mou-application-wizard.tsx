"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { 
  Save, 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react"
import { 
  mouApplicationService, 
  MouApplication, 
  ApplicationStatus,
  Step1Data,
  Step2Data,
  Step3Data,
  Step4Data,
  Step5Data
} from "@/lib/services/mou-application.service"

// Import step components
import { Step1MouInformation } from "./wizard-steps/step1-mou-information"
import { Step2PartyDetails } from "./wizard-steps/step2-party-details"
import { Step3ProjectInformation } from "./wizard-steps/step3-project-information"
import { Step4DocumentUpload } from "./wizard-steps/step4-document-upload"
import { Step5ReviewSubmit } from "./wizard-steps/step5-review-submit"

interface MouApplicationWizardProps {
  applicationId?: string // If provided, edit existing application
  onComplete?: (application: MouApplication) => void
  onCancel?: () => void
}

const STEPS = [
  { number: 1, title: "MoU Information", description: "Select MoU and set duration" },
  { number: 2, title: "Party Details", description: "Signatory and responsibilities" },
  { number: 3, title: "Project Information", description: "Projects and activities" },
  { number: 4, title: "Document Upload", description: "Supporting documents" },
  { number: 5, title: "Review & Submit", description: "Final review and submission" }
]

export function MouApplicationWizard({ applicationId, onComplete, onCancel }: MouApplicationWizardProps) {
  const { user } = useAuth()
  const router = useRouter()
  
  // State management
  const [currentStep, setCurrentStep] = useState(1)
  const [application, setApplication] = useState<MouApplication | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState("")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  
  // Step data
  const [step1Data, setStep1Data] = useState<Step1Data>({
    mouDurationYears: 1,
    organizationName: user?.organization?.organizationName || ""
  })
  const [step2Data, setStep2Data] = useState<Step2Data>({
    partyName: user?.organization?.organizationName || "",
    signatoryName: "",
    signatoryPosition: "",
    responsibilities: [""]
  })
  const [step3Data, setStep3Data] = useState<Step3Data>({
    projects: []
  })
  const [step4Data, setStep4Data] = useState<Step4Data>({
    documents: []
  })
  const [step5Data, setStep5Data] = useState<Step5Data>({
    termsAccepted: false,
    finalReview: false
  })

  // Auto-save functionality
  const autoSaveInterval = useCallback(() => {
    if (application && hasUnsavedChanges) {
      handleAutoSave()
    }
  }, [application, hasUnsavedChanges])

  useEffect(() => {
    const interval = setInterval(autoSaveInterval, 30000) // Auto-save every 30 seconds
    return () => clearInterval(interval)
  }, [autoSaveInterval])

  // Load existing application if editing
  useEffect(() => {
    if (applicationId) {
      loadApplication()
    }
  }, [applicationId])

  const loadApplication = async () => {
    try {
      setLoading(true)
      const app = await mouApplicationService.getApplication(applicationId!)
      setApplication(app)
      setCurrentStep(app.currentStep)
      
      // Populate form data from application
      populateFormData(app)
    } catch (err: any) {
      setError("Failed to load application")
    } finally {
      setLoading(false)
    }
  }

  const populateFormData = (app: MouApplication) => {
    // Step 1
    setStep1Data({
      mouDurationYears: app.mouDurationYears || 1,
      organizationName: user?.organization?.organizationName || ""
    })

    // Step 2
    setStep2Data({
      partyName: app.partyName || user?.organization?.organizationName || "",
      signatoryName: app.signatoryName || "",
      signatoryPosition: app.signatoryPosition || "",
      responsibilities: app.responsibilities?.map(r => r.responsibilityText) || [""]
    })

    // Step 3
    setStep3Data({
      projects: app.projects?.map(p => ({
        id: p.id,
        projectName: p.projectName,
        projectDescription: p.projectDescription,
        startDate: p.startDate?.split('T')[0],
        endDate: p.endDate?.split('T')[0],
        totalBudget: p.totalBudget,
        currency: p.currency || "USD",
        activities: p.activities?.map(a => ({
          id: a.id,
          activityName: a.activityName,
          description: a.description,
          timeline: a.timeline,
          budgetAllocation: a.budgetAllocation,
          currency: a.currency || "USD"
        })) || []
      })) || []
    })

    // Step 4 - documents are loaded separately
    // Step 5 - review data
  }

  const handleAutoSave = async () => {
    if (!application) return

    try {
      setSaving(true)
      const currentStepData = getCurrentStepData()
      await mouApplicationService.autoSave(application.id, {
        currentStep,
        ...currentStepData
      })
      setHasUnsavedChanges(false)
    } catch (err) {
      console.error("Auto-save failed:", err)
    } finally {
      setSaving(false)
    }
  }

  const handleSaveDraft = async () => {
    try {
      setSaving(true)
      setError("")

      if (!application) {
        // Create new draft - use a default mouId or handle this differently
        // For now, we'll create a draft without mouId and set it later
        const newApp = await mouApplicationService.createDraft("default-mou-id")
        setApplication(newApp)
      }

      const currentStepData = getCurrentStepData()
      const updatedApp = await mouApplicationService.updateStep(
        application!.id,
        currentStep,
        currentStepData
      )

      setApplication(updatedApp)
      setHasUnsavedChanges(false)
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to save draft")
    } finally {
      setSaving(false)
    }
  }

  const getCurrentStepData = () => {
    switch (currentStep) {
      case 1: return step1Data
      case 2: return step2Data
      case 3: return step3Data
      case 4: return step4Data
      case 5: return step5Data
      default: return {}
    }
  }

  const validateCurrentStep = async (): Promise<boolean> => {
    if (!application) return false

    try {
      const validation = await mouApplicationService.validateStep(application.id, currentStep)
      if (!validation.isValid) {
        setError(validation.errors.join(", "))
        return false
      }
      return true
    } catch (err) {
      return true // Allow progression if validation service fails
    }
  }

  const handleNext = async () => {
    const isValid = await validateCurrentStep()
    if (!isValid) return

    await handleSaveDraft()
    
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleStepClick = (stepNumber: number) => {
    if (stepNumber <= currentStep || application?.status === 'DRAFT') {
      setCurrentStep(stepNumber)
    }
  }

  const handleSubmit = async () => {
    if (!application) return

    try {
      setLoading(true)
      await mouApplicationService.submitApplication(application.id)
      
      if (onComplete) {
        onComplete(application)
      } else {
        router.push(`/dashboard/partner/applications/${application.id}`)
      }
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to submit application")
    } finally {
      setLoading(false)
    }
  }

  const getStepStatus = (stepNumber: number) => {
    if (stepNumber < currentStep) return "completed"
    if (stepNumber === currentStep) return "current"
    return "upcoming"
  }

  const progressPercentage = application ? 
    mouApplicationService.calculateCompletionPercentage(application) : 
    (currentStep / 5) * 100

  if (loading && !application) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading application...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold text-cyan-900">
                {applicationId ? "Edit MoU Application" : "New MoU Application"}
              </CardTitle>
              <CardDescription>
                Step {currentStep} of 5: {STEPS[currentStep - 1].description}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {saving && (
                <Badge variant="outline" className="text-amber-600 border-amber-200">
                  <Clock className="h-3 w-3 mr-1" />
                  Saving...
                </Badge>
              )}
              {application?.status === 'DRAFT' && (
                <Badge variant="outline" className="text-gray-600 border-gray-200">
                  Draft
                </Badge>
              )}
            </div>
          </div>
          
          <div className="space-y-4">
            <Progress value={progressPercentage} className="w-full" />
            
            {/* Step indicators */}
            <div className="flex items-center justify-between">
              {STEPS.map((step) => {
                const status = getStepStatus(step.number)
                return (
                  <button
                    key={step.number}
                    onClick={() => handleStepClick(step.number)}
                    className={`flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors ${
                      status === 'current' 
                        ? 'bg-cyan-50 text-cyan-700' 
                        : status === 'completed'
                        ? 'bg-green-50 text-green-700 hover:bg-green-100'
                        : 'text-gray-500 hover:bg-gray-50'
                    }`}
                    disabled={status === 'upcoming' && application?.status !== 'DRAFT'}
                  >
                    <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                      status === 'current'
                        ? 'border-cyan-600 bg-cyan-600 text-white'
                        : status === 'completed'
                        ? 'border-green-600 bg-green-600 text-white'
                        : 'border-gray-300'
                    }`}>
                      {status === 'completed' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        step.number
                      )}
                    </div>
                    <span className="text-xs font-medium">{step.title}</span>
                  </button>
                )
              })}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {currentStep === 1 && (
            <Step1MouInformation
              data={step1Data}
              onChange={(data) => {
                setStep1Data(data)
                setHasUnsavedChanges(true)
              }}
              application={application}
            />
          )}
          {currentStep === 2 && (
            <Step2PartyDetails
              data={step2Data}
              onChange={(data) => {
                setStep2Data(data)
                setHasUnsavedChanges(true)
              }}
              application={application}
            />
          )}
          {currentStep === 3 && (
            <Step3ProjectInformation
              data={step3Data}
              onChange={(data) => {
                setStep3Data(data)
                setHasUnsavedChanges(true)
              }}
              application={application}
              mouDurationYears={step1Data.mouDurationYears}
            />
          )}
          {currentStep === 4 && (
            <Step4DocumentUpload
              data={step4Data}
              onChange={(data) => {
                setStep4Data(data)
                setHasUnsavedChanges(true)
              }}
              application={application}
            />
          )}
          {currentStep === 5 && (
            <Step5ReviewSubmit
              data={step5Data}
              onChange={(data) => {
                setStep5Data(data)
                setHasUnsavedChanges(true)
              }}
              application={application}
              allStepData={{
                step1: step1Data,
                step2: step2Data,
                step3: step3Data,
                step4: step4Data,
                step5: step5Data
              }}
              onSubmit={handleSubmit}
            />
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {currentStep > 1 && (
                <Button variant="outline" onClick={handlePrevious}>
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
              )}
              {onCancel && (
                <Button variant="ghost" onClick={onCancel}>
                  Cancel
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={saving}
              >
                <Save className="h-4 w-4 mr-1" />
                Save Draft
              </Button>
              
              {currentStep < 5 ? (
                <Button
                  onClick={handleNext}
                  disabled={saving}
                  className="bg-cyan-600 hover:bg-cyan-700"
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={loading || !step5Data.termsAccepted}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Submit Application
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
