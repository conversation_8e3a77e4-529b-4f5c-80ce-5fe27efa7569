---
applyTo: '/MoU/server'
---
Coding standards, domain knowledge, and preferences that AI should follow.

🏥 Project Description: MoU Management System for Ministry of Health
This project is a backend system for a Memorandum of Understanding (MoU) management web application developed for the Ministry of Health. It enables centralized management of MoUs, users, and related metadata. The system supports secure user authentication, comprehensive data handling, and email notifications for key actions. Only admin users can manage core system data, while read-only access may be available to others where appropriate.

⚙️ Backend Stack & Tools
Framework: NestJS

ORM: Prisma (use the existing schema in the /prisma folder as reference)

Authentication: JWT-based auth (with refresh token strategy optional)

Email: Handlebars templates with Roboto font & cyan primary color

SMTP Server: MailHog (for local development and testing)

Documentation: Swagger (auto-generated from controllers and DTOs)

✅ Implementation Scope
1. Authentication Module
JWT-based login and signup

Email verification flow

Password reset functionality

Email templates:

Designed using Handlebars

Use Roboto font and cyan as the primary brand color

Clean, professional, minimal design

Mail sending via local MailHog SMTP

2. User Management
Full CRUD for user accounts

Access control:

<PERSON><PERSON> can create, update, and delete users

Regular users have read-only access (where applicable)

3. Master Data Modules (Admin CRUD + Public GET)
Implement secured CRUD APIs for the following entities:

Domain Intervention

Budget Types

Funding Source

Funding Unit

Organization Types

Input Category

Financing Scheme

Financing Agent

Health Care Provider

Currency

Each module should follow NestJS best practices with:

DTOs (Data Transfer Objects)

Services

Controllers

Swagger documentation for all endpoints

📌 Development Guidelines
Start with the Auth module, then proceed to User Management and remaining master data modules.

Leverage and update the existing NestJS codebase — do not rewrite unless necessary.

Use and extend the existing Prisma schema where needed.

All endpoints must be documented with Swagger decorators for visibility and testing.