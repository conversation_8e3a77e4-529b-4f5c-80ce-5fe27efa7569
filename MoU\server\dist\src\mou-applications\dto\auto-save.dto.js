"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AutoSaveDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class AutoSaveDto {
}
exports.AutoSaveDto = AutoSaveDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current step being worked on (1-5)',
        minimum: 1,
        maximum: 5,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], AutoSaveDto.prototype, "currentStep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Completion percentage (0-100)',
        minimum: 0,
        maximum: 100,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], AutoSaveDto.prototype, "completionPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Form data to save (JSON)',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], AutoSaveDto.prototype, "formData", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application responsibility description',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AutoSaveDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU duration in years',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], AutoSaveDto.prototype, "mouDurationYears", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the signatory',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AutoSaveDto.prototype, "signatoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Position of the signatory',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AutoSaveDto.prototype, "signatoryPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party name for this application',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AutoSaveDto.prototype, "partyName", void 0);
//# sourceMappingURL=auto-save.dto.js.map