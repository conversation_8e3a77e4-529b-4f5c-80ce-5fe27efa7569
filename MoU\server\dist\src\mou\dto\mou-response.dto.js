"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class MouResponseDto {
}
exports.MouResponseDto = MouResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the MoU',
        example: 'uuid-mou-id'
    }),
    __metadata("design:type", String)
], MouResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU identifier',
        example: 'MOU-2024-001'
    }),
    __metadata("design:type", String)
], MouResponseDto.prototype, "mouId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party ID associated with this MoU',
        example: 'uuid-party-id'
    }),
    __metadata("design:type", String)
], MouResponseDto.prototype, "partyId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp',
        example: '2024-01-01T00:00:00.000Z'
    }),
    __metadata("design:type", String)
], MouResponseDto.prototype, "createAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp',
        example: '2024-01-01T00:00:00.000Z'
    }),
    __metadata("design:type", String)
], MouResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Soft delete flag',
        example: false
    }),
    __metadata("design:type", Boolean)
], MouResponseDto.prototype, "deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated party information',
        required: false
    }),
    __metadata("design:type", Object)
], MouResponseDto.prototype, "party", void 0);
//# sourceMappingURL=mou-response.dto.js.map