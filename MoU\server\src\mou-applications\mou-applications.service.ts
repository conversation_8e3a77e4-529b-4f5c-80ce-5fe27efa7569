import { Injectable, NotFoundException, ConflictException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateMouApplicationDto, UpdateMouApplicationDto, UpdateStepDto, AutoSaveDto, CreateResponsibilityDto, UploadDocumentDto } from './dto';
import { ApplicationStatus } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class MouApplicationsService {
  constructor(private prisma: PrismaService) {}

  async create(createMouApplicationDto: CreateMouApplicationDto, userId: string) {
    // Verify user exists
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if application ID already exists
    const existingApplication = await this.prisma.mouApplication.findUnique({
      where: { mouApplicationId: createMouApplicationDto.mouApplicationId }
    });

    if (existingApplication) {
      throw new ConflictException('Application with this ID already exists');
    }

    // Verify MoU exists
    const mou = await this.prisma.mou.findUnique({
      where: { id: createMouApplicationDto.mouId }
    });

    if (!mou) {
      throw new NotFoundException('MoU not found');
    }

    return this.prisma.mouApplication.create({
      data: {
        ...createMouApplicationDto,
        userId,
        status: createMouApplicationDto.status || ApplicationStatus.DRAFT,
        currentStep: createMouApplicationDto.currentStep || 1,
        completionPercentage: createMouApplicationDto.completionPercentage || 0,
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: true,
        responsibilities: true,
        documents: true,
        approvalSteps: true,
      }
    });
  }

  async createDraft(mouId: string, userId: string) {
    // Generate unique application ID
    const applicationId = `APP-${new Date().getFullYear()}-${Date.now()}`;
    
    return this.create({
      mouApplicationId: applicationId,
      mouId,
      status: ApplicationStatus.DRAFT,
      currentStep: 1,
      completionPercentage: 0,
    }, userId);
  }

  async findAll(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Admin can see all applications, others see only their own
    const whereClause = user.role === 'ADMIN' 
      ? { deleted: false }
      : { deleted: false, userId };

    return this.prisma.mouApplication.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: true,
        responsibilities: {
          orderBy: { order: 'asc' }
        },
        documents: true,
        approvalSteps: true,
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  async findMyApplications(userId: string) {
    return this.prisma.mouApplication.findMany({
      where: { 
        deleted: false, 
        userId 
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: true,
        responsibilities: {
          orderBy: { order: 'asc' }
        },
        documents: true,
        approvalSteps: true,
      },
      orderBy: { createdAt: 'desc' }
    });
  }

  async findOne(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const application = await this.prisma.mouApplication.findUnique({
      where: { id, deleted: false },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: {
          include: {
            projectActivities: true
          }
        },
        responsibilities: {
          orderBy: { order: 'asc' }
        },
        documents: true,
        approvalSteps: {
          include: {
            reviewer: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
                role: true,
              }
            }
          }
        },
      }
    });

    if (!application) {
      throw new NotFoundException('Application not found');
    }

    // Check access permissions
    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return application;
  }

  async update(id: string, updateMouApplicationDto: UpdateMouApplicationDto, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner or admin can update
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check for application ID conflicts if updating
    if (updateMouApplicationDto.mouApplicationId &&
        updateMouApplicationDto.mouApplicationId !== application.mouApplicationId) {
      const existingApplication = await this.prisma.mouApplication.findUnique({
        where: { mouApplicationId: updateMouApplicationDto.mouApplicationId }
      });

      if (existingApplication) {
        throw new ConflictException('Application with this ID already exists');
      }
    }

    return this.prisma.mouApplication.update({
      where: { id },
      data: updateMouApplicationDto,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: true,
        responsibilities: {
          orderBy: { order: 'asc' }
        },
        documents: true,
        approvalSteps: true,
      }
    });
  }

  async updateStep(id: string, stepNumber: number, updateStepDto: UpdateStepDto, userId: string) {
    const application = await this.findOne(id, userId);

    // Validate step number
    if (stepNumber < 1 || stepNumber > 5) {
      throw new BadRequestException('Step number must be between 1 and 5');
    }

    // Only the owner can update steps (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Update the application with step-specific data
    const updateData: any = {
      currentStep: stepNumber,
      lastAutoSave: new Date(),
    };

    // Add step-specific fields
    if (updateStepDto.responsibility !== undefined) updateData.responsibility = updateStepDto.responsibility;
    if (updateStepDto.mouDurationYears !== undefined) updateData.mouDurationYears = updateStepDto.mouDurationYears;
    if (updateStepDto.signatoryName !== undefined) updateData.signatoryName = updateStepDto.signatoryName;
    if (updateStepDto.signatoryPosition !== undefined) updateData.signatoryPosition = updateStepDto.signatoryPosition;
    if (updateStepDto.partyName !== undefined) updateData.partyName = updateStepDto.partyName;
    if (updateStepDto.completionPercentage !== undefined) updateData.completionPercentage = updateStepDto.completionPercentage;
    if (updateStepDto.status !== undefined) updateData.status = updateStepDto.status;

    return this.prisma.mouApplication.update({
      where: { id },
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            organization: {
              select: {
                id: true,
                organizationName: true,
              }
            }
          }
        },
        mou: {
          select: {
            id: true,
            mouId: true,
            party: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        },
        projects: true,
        responsibilities: {
          orderBy: { order: 'asc' }
        },
        documents: true,
        approvalSteps: true,
      }
    });
  }

  async autoSave(id: string, autoSaveDto: AutoSaveDto, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can auto-save (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    const updateData: any = {
      lastAutoSave: new Date(),
    };

    // Add auto-save fields
    if (autoSaveDto.currentStep !== undefined) updateData.currentStep = autoSaveDto.currentStep;
    if (autoSaveDto.completionPercentage !== undefined) updateData.completionPercentage = autoSaveDto.completionPercentage;
    if (autoSaveDto.responsibility !== undefined) updateData.responsibility = autoSaveDto.responsibility;
    if (autoSaveDto.mouDurationYears !== undefined) updateData.mouDurationYears = autoSaveDto.mouDurationYears;
    if (autoSaveDto.signatoryName !== undefined) updateData.signatoryName = autoSaveDto.signatoryName;
    if (autoSaveDto.signatoryPosition !== undefined) updateData.signatoryPosition = autoSaveDto.signatoryPosition;
    if (autoSaveDto.partyName !== undefined) updateData.partyName = autoSaveDto.partyName;

    return this.prisma.mouApplication.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        lastAutoSave: true,
        currentStep: true,
        completionPercentage: true,
      }
    });
  }

  async createResponsibility(id: string, createResponsibilityDto: CreateResponsibilityDto, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can add responsibilities (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.applicationResponsibility.create({
      data: {
        ...createResponsibilityDto,
        mouApplicationId: id,
      }
    });
  }

  async updateResponsibility(id: string, responsibilityId: string, updateData: Partial<CreateResponsibilityDto>, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can update responsibilities (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Verify responsibility belongs to this application
    const responsibility = await this.prisma.applicationResponsibility.findFirst({
      where: {
        id: responsibilityId,
        mouApplicationId: id,
        deleted: false
      }
    });

    if (!responsibility) {
      throw new NotFoundException('Responsibility not found');
    }

    return this.prisma.applicationResponsibility.update({
      where: { id: responsibilityId },
      data: updateData
    });
  }

  async deleteResponsibility(id: string, responsibilityId: string, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can delete responsibilities (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Verify responsibility belongs to this application
    const responsibility = await this.prisma.applicationResponsibility.findFirst({
      where: {
        id: responsibilityId,
        mouApplicationId: id,
        deleted: false
      }
    });

    if (!responsibility) {
      throw new NotFoundException('Responsibility not found');
    }

    // Soft delete
    await this.prisma.applicationResponsibility.update({
      where: { id: responsibilityId },
      data: { deleted: true }
    });

    return { message: 'Responsibility deleted successfully' };
  }

  async uploadDocument(id: string, file: Express.Multer.File, uploadDocumentDto: UploadDocumentDto, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can upload documents (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // Generate unique document ID
    const documentId = `DOC-${Date.now()}-${uuidv4()}`;

    return this.prisma.applicationDocument.create({
      data: {
        documentId,
        fileName: file.filename,
        originalName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        filePath: file.path,
        documentType: uploadDocumentDto.documentType,
        isRequired: uploadDocumentDto.isRequired || false,
        mouApplicationId: id,
      }
    });
  }

  async deleteDocument(id: string, documentId: string, userId: string) {
    const application = await this.findOne(id, userId);

    // Only the owner can delete documents (unless admin)
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Verify document belongs to this application
    const document = await this.prisma.applicationDocument.findFirst({
      where: {
        id: documentId,
        mouApplicationId: id,
        deleted: false
      }
    });

    if (!document) {
      throw new NotFoundException('Document not found');
    }

    // Delete physical file
    try {
      if (fs.existsSync(document.filePath)) {
        fs.unlinkSync(document.filePath);
      }
    } catch (error) {
      console.error('Error deleting file:', error);
    }

    // Soft delete from database
    await this.prisma.applicationDocument.update({
      where: { id: documentId },
      data: { deleted: true }
    });

    return { message: 'Document deleted successfully' };
  }

  async remove(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const application = await this.findOne(id, userId);

    // Only admin or owner can delete
    if (user.role !== 'ADMIN' && application.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Check if application can be deleted (only drafts)
    if (application.status !== ApplicationStatus.DRAFT) {
      throw new BadRequestException('Only draft applications can be deleted');
    }

    // Delete associated documents from filesystem
    const documents = await this.prisma.applicationDocument.findMany({
      where: { mouApplicationId: id, deleted: false }
    });

    for (const document of documents) {
      try {
        if (fs.existsSync(document.filePath)) {
          fs.unlinkSync(document.filePath);
        }
      } catch (error) {
        console.error('Error deleting file:', error);
      }
    }

    // Soft delete the application (cascade will handle related records)
    await this.prisma.mouApplication.update({
      where: { id },
      data: { deleted: true }
    });

    return { message: 'Application deleted successfully' };
  }
}
