"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, MoreHorizontal, Search, Edit, Trash2 } from "lucide-react"
import { masterDataService, type FinancingScheme } from "@/lib/services/master-data.service"

export default function FinancingSchemesPage() {
  const [schemes, setSchemes] = useState<FinancingScheme[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingScheme, setEditingScheme] = useState<FinancingScheme | null>(null)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  // Form state
  const [schemeName, setSchemeName] = useState("")
  const [description, setDescription] = useState("")
  const [terms, setTerms] = useState("")
  const [conditions, setConditions] = useState("")

  useEffect(() => {
    loadSchemes()
  }, [])

  const loadSchemes = async () => {
    try {
      setLoading(true)
      const data = await masterDataService.getFinancingSchemes()
      setSchemes(data)
    } catch (error) {
      console.error("Failed to load financing schemes:", error)
      setError("Failed to load financing schemes")
    } finally {
      setLoading(false)
    }
  }

  const filteredSchemes = schemes.filter((scheme) =>
    scheme.schemeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (scheme.description || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
    (scheme.terms || "").toLowerCase().includes(searchTerm.toLowerCase())
  )

  const resetForm = () => {
    setSchemeName("")
    setDescription("")
    setTerms("")
    setConditions("")
    setEditingScheme(null)
    setError("")
    setSuccess("")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")

    try {
      const schemeData = {
        schemeName,
        description: description || undefined,
        terms: terms || undefined,
        conditions: conditions || undefined,
      }

      if (editingScheme) {
        await masterDataService.updateFinancingScheme(editingScheme.id, schemeData)
        setSuccess("Financing scheme updated successfully")
      } else {
        await masterDataService.createFinancingScheme(schemeData)
        setSuccess("Financing scheme created successfully")
      }

      await loadSchemes()
      setDialogOpen(false)
      resetForm()
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to save financing scheme")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (scheme: FinancingScheme) => {
    setEditingScheme(scheme)
    setSchemeName(scheme.schemeName)
    setDescription(scheme.description || "")
    setTerms(scheme.terms || "")
    setConditions(scheme.conditions || "")
    setDialogOpen(true)
  }

  const handleDelete = async (scheme: FinancingScheme) => {
    if (confirm("Are you sure you want to delete this financing scheme?")) {
      try {
        await masterDataService.deleteFinancingScheme(scheme.id)
        setSuccess("Financing scheme deleted successfully")
        await loadSchemes()
      } catch (error: any) {
        setError(error.response?.data?.message || "Failed to delete financing scheme")
      }
    }
  }

  const openCreateDialog = () => {
    resetForm()
    setDialogOpen(true)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Financing Schemes</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="mr-2 h-4 w-4" /> Add Financing Scheme
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{editingScheme ? "Edit Financing Scheme" : "Create New Financing Scheme"}</DialogTitle>
              <DialogDescription>
                {editingScheme ? "Update financing scheme information." : "Add a new financing scheme to the system."}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="schemeName">Scheme Name *</Label>
                  <Input
                    id="schemeName"
                    value={schemeName}
                    onChange={(e) => setSchemeName(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="terms">Terms and Conditions</Label>
                  <Textarea
                    id="terms"
                    value={terms}
                    onChange={(e) => setTerms(e.target.value)}
                    rows={3}
                    placeholder="Maximum amount, duration, requirements, etc."
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="conditions">Additional Conditions</Label>
                  <Textarea
                    id="conditions"
                    value={conditions}
                    onChange={(e) => setConditions(e.target.value)}
                    rows={2}
                    placeholder="Special requirements, eligibility criteria, etc."
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingScheme ? "Updating..." : "Creating..."}
                    </>
                  ) : editingScheme ? (
                    "Update Scheme"
                  ) : (
                    "Create Scheme"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && !dialogOpen && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search financing schemes..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Scheme Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Terms</TableHead>
                <TableHead>Created At</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSchemes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                    No financing schemes found
                  </TableCell>
                </TableRow>
              ) : (
                filteredSchemes.map((scheme) => (
                  <TableRow key={scheme.id}>
                    <TableCell className="font-medium">{scheme.schemeName}</TableCell>
                    <TableCell className="max-w-xs truncate">{scheme.description || "N/A"}</TableCell>
                    <TableCell className="max-w-xs truncate">{scheme.terms || "N/A"}</TableCell>
                    <TableCell>{new Date(scheme.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleEdit(scheme)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDelete(scheme)} className="text-red-600">
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  )
}
