"use client"

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { MouApplicationWizard } from "@/components/mou-application-wizard"
import { MouApplication } from "@/lib/services/mou-application.service"

export default function EditMouApplicationPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const applicationId = params.id as string

  const handleComplete = (application: MouApplication) => {
    router.push(`/dashboard/partner/applications/${application.id}`)
  }

  const handleCancel = () => {
    router.push(`/dashboard/partner/applications/${applicationId}`)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href={`/dashboard/partner/applications/${applicationId}`}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Application
          </Link>
        </Button>
      </div>

      <MouApplicationWizard
        applicationId={applicationId}
        onComplete={handleComplete}
        onCancel={handleCancel}
      />
    </div>
  )
}
