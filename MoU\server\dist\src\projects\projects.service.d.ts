import { PrismaService } from '../prisma/prisma.service';
import { CreateProjectDto, UpdateProjectDto, CreateProjectActivityDto, UpdateProjectActivityDto } from './dto';
export declare class ProjectsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createProjectDto: CreateProjectDto, userId: string): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    findAll(userId: string): Promise<({
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    })[]>;
    findOne(id: string, userId: string): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        mouApplication: {
            id: string;
            userId: string;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApplicationStatus;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    update(id: string, updateProjectDto: UpdateProjectDto, userId: string): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    createActivity(projectId: string, createProjectActivityDto: CreateProjectActivityDto, userId: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    findActivities(projectId: string, userId: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }[]>;
    findActivity(projectId: string, activityId: string, userId: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    updateActivity(projectId: string, activityId: string, updateProjectActivityDto: UpdateProjectActivityDto, userId: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    deleteActivity(projectId: string, activityId: string, userId: string): Promise<{
        message: string;
    }>;
    remove(id: string, userId: string): Promise<{
        message: string;
    }>;
}
