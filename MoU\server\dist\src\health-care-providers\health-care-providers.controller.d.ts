import { HealthCareProvidersService } from './health-care-providers.service';
import { CreateHealthCareProviderDto, UpdateHealthCareProviderDto } from './dto';
export declare class HealthCareProvidersController {
    private readonly healthCareProvidersService;
    constructor(healthCareProvidersService: HealthCareProvidersService);
    create(createHealthCareProviderDto: CreateHealthCareProviderDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    update(id: number, updateHealthCareProviderDto: UpdateHealthCareProviderDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
    remove(id: number, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        providerName: string;
        location: string | null;
        contactEmail: string | null;
        contactPhone: string | null;
        website: string | null;
    }>;
}
