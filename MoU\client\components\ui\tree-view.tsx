"use client"

import Re<PERSON>, { useState, use<PERSON><PERSON>back, useMemo } from "react"
import { ChevronDown, ChevronRight, Edit, Trash2, Plus, MoreHorizontal, Folder<PERSON><PERSON>, Folder } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"

export interface TreeNode {
  id: number
  name: string
  description?: string
  parentId?: number
  children?: TreeNode[]
  [key: string]: any
}

interface TreeViewProps {
  data: TreeNode[]
  onEdit?: (node: TreeNode) => void
  onDelete?: (node: TreeNode) => void
  onAddChild?: (parentNode: TreeNode) => void
  nameKey?: string
  className?: string
  defaultExpandAll?: boolean
  expandedNodes?: Set<number>
  onExpandedChange?: (expandedNodes: Set<number>) => void
}

interface TreeNodeProps {
  node: TreeNode
  level: number
  onEdit?: (node: TreeNode) => void
  onDelete?: (node: TreeNode) => void
  onAddChild?: (parentNode: TreeNode) => void
  nameKey: string
  expandedNodes: Set<number>
  onToggleExpand: (nodeId: number) => void
}

const TreeNodeComponent: React.FC<TreeNodeProps> = ({
  node,
  level,
  onEdit,
  onDelete,
  onAddChild,
  nameKey,
  expandedNodes,
  onToggleExpand,
}) => {
  const hasChildren = node.children && node.children.length > 0
  const isExpanded = expandedNodes.has(node.id)

  const handleToggle = useCallback(() => {
    if (hasChildren) {
      onToggleExpand(node.id)
    }
  }, [hasChildren, node.id, onToggleExpand])

  const paddingLeft = level * 20

  return (
    <div className="select-none">
      <div
        className="flex items-center py-2 px-2 hover:bg-gray-50 rounded-md group relative transition-colors duration-150"
        style={{ paddingLeft: `${paddingLeft}px` }}
      >
        {/* Hierarchy visual indicators */}
        {level > 0 && (
          <div className="absolute left-0 top-0 bottom-0 flex">
            {Array.from({ length: level }, (_, i) => (
              <div
                key={i}
                className="w-5 flex justify-center"
                style={{ left: `${i * 20}px` }}
              >
                <div className="w-px bg-gray-200 h-full" />
              </div>
            ))}
            <div
              className="w-5 h-6 border-l border-b border-gray-200 rounded-bl"
              style={{ left: `${(level - 1) * 20}px`, top: '50%', transform: 'translateY(-50%)' }}
            />
          </div>
        )}
        <div className="flex items-center flex-1 min-w-0">
          {/* Expand/Collapse Button */}
          <button
            onClick={handleToggle}
            className="flex items-center justify-center w-6 h-6 mr-2 hover:bg-gray-200 rounded transition-colors duration-150"
            disabled={!hasChildren}
          >
            {hasChildren ? (
              isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              )
            ) : (
              <div className="w-4 h-4" />
            )}
          </button>

          {/* Folder Icon */}
          <div className="mr-2">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpen className="w-4 h-4 text-blue-500" />
              ) : (
                <Folder className="w-4 h-4 text-blue-500" />
              )
            ) : (
              <div className="w-4 h-4 bg-gray-300 rounded-sm" />
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <div className="font-medium text-sm truncate">
                {node[nameKey] || node.name}
              </div>
              <div className="flex items-center gap-1">
                {level > 0 && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    L{level}
                  </Badge>
                )}
                {hasChildren && (
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                    {node.children?.length || 0}
                  </Badge>
                )}
              </div>
            </div>
            {node.description && (
              <div className="text-xs text-gray-500 truncate mt-0.5">
                {node.description}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons - Always visible for better UX */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
          {/* Quick Edit Button */}
          {onEdit && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 hover:bg-blue-100"
              onClick={() => onEdit(node)}
              title="Edit"
            >
              <Edit className="w-3 h-3 text-blue-600" />
            </Button>
          )}

          {/* Quick Add Child Button */}
          {onAddChild && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 w-7 p-0 hover:bg-green-100"
              onClick={() => onAddChild(node)}
              title="Add Child"
            >
              <Plus className="w-3 h-3 text-green-600" />
            </Button>
          )}

          {/* More Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 w-7 p-0 hover:bg-gray-100">
                <span className="sr-only">More actions</span>
                <MoreHorizontal className="w-3 h-3 text-gray-600" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions for "{node[nameKey] || node.name}"</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {onEdit && (
                <DropdownMenuItem onClick={() => onEdit(node)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Details
                </DropdownMenuItem>
              )}
              {onAddChild && (
                <DropdownMenuItem onClick={() => onAddChild(node)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Sub-Domain
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {hasChildren && (
                <DropdownMenuItem onClick={handleToggle}>
                  {isExpanded ? (
                    <>
                      <ChevronRight className="mr-2 h-4 w-4" />
                      Collapse
                    </>
                  ) : (
                    <>
                      <ChevronDown className="mr-2 h-4 w-4" />
                      Expand
                    </>
                  )}
                </DropdownMenuItem>
              )}
              {onDelete && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => onDelete(node)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {hasChildren && isExpanded && (
        <div className="ml-2 border-l border-gray-200">
          {node.children!.map((child, index) => (
            <div key={child.id} className={index === node.children!.length - 1 ? "border-l-transparent" : ""}>
              <TreeNodeComponent
                node={child}
                level={level + 1}
                onEdit={onEdit}
                onDelete={onDelete}
                onAddChild={onAddChild}
                nameKey={nameKey}
                expandedNodes={expandedNodes}
                onToggleExpand={onToggleExpand}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export const TreeView: React.FC<TreeViewProps> = ({
  data,
  onEdit,
  onDelete,
  onAddChild,
  nameKey = "name",
  className = "",
  defaultExpandAll = true,
  expandedNodes: controlledExpandedNodes,
  onExpandedChange,
}) => {
  // Internal state for expanded nodes
  const [internalExpandedNodes, setInternalExpandedNodes] = useState<Set<number>>(() => {
    if (defaultExpandAll) {
      const getAllNodeIds = (nodes: TreeNode[]): number[] => {
        const ids: number[] = []
        nodes.forEach(node => {
          ids.push(node.id)
          if (node.children) {
            ids.push(...getAllNodeIds(node.children))
          }
        })
        return ids
      }
      return new Set(getAllNodeIds(data))
    }
    return new Set()
  })

  // Use controlled or internal state
  const expandedNodes = controlledExpandedNodes || internalExpandedNodes
  const setExpandedNodes = onExpandedChange || setInternalExpandedNodes

  const handleToggleExpand = useCallback((nodeId: number) => {
    const newExpandedNodes = new Set(expandedNodes)
    if (newExpandedNodes.has(nodeId)) {
      newExpandedNodes.delete(nodeId)
    } else {
      newExpandedNodes.add(nodeId)
    }
    setExpandedNodes(newExpandedNodes)
  }, [expandedNodes, setExpandedNodes])

  // Memoize the tree rendering for performance
  const renderTree = useMemo(() => {
    if (!data || data.length === 0) {
      return (
        <div className={`text-center py-8 text-gray-500 ${className}`}>
          <div className="flex flex-col items-center gap-2">
            <Folder className="w-8 h-8 text-gray-300" />
            <span>No items found</span>
          </div>
        </div>
      )
    }

    return (
      <div className={`space-y-1 ${className}`}>
        {data.map((node) => (
          <TreeNodeComponent
            key={node.id}
            node={node}
            level={0}
            onEdit={onEdit}
            onDelete={onDelete}
            onAddChild={onAddChild}
            nameKey={nameKey}
            expandedNodes={expandedNodes}
            onToggleExpand={handleToggleExpand}
          />
        ))}
      </div>
    )
  }, [data, className, onEdit, onDelete, onAddChild, nameKey, expandedNodes, handleToggleExpand])

  return renderTree
}
