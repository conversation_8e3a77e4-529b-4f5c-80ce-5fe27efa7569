"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, CheckCircle, XCircle } from "lucide-react"

export default function VerifyAccountPage() {
  const [error, setError] = useState("")
  const [success, setSuccess] = useState(false)
  const [isVerifying, setIsVerifying] = useState(true)
  const { verifyAccount } = useAuth()
  const params = useParams()
  const token = params.token as string

  useEffect(() => {
    const verify = async () => {
      try {
        await verifyAccount(token)
        setSuccess(true)
      } catch (err) {
        setError("Invalid or expired verification token")
      } finally {
        setIsVerifying(false)
      }
    }

    if (token) {
      verify()
    } else {
      setError("No verification token provided")
      setIsVerifying(false)
    }
  }, [token, verifyAccount])

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">Verify Account</CardTitle>
          <CardDescription>Verifying your email address</CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center space-y-4 pt-4">
          {isVerifying ? (
            <div className="flex flex-col items-center space-y-4">
              <Loader2 className="h-16 w-16 animate-spin text-primary" />
              <p className="text-center text-muted-foreground">Verifying your account...</p>
            </div>
          ) : success ? (
            <div className="flex flex-col items-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500" />
              <Alert>
                <AlertDescription>Your account has been successfully verified! You can now log in.</AlertDescription>
              </Alert>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-4">
              <XCircle className="h-16 w-16 text-red-500" />
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </div>
          )}
        </CardContent>
        <CardFooter>
          <div className="flex w-full justify-center">
            <Button asChild disabled={isVerifying}>
              <Link href="/login">Go to Login</Link>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
