"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrenciesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const currencies_service_1 = require("./currencies.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let CurrenciesController = class CurrenciesController {
    constructor(currenciesService) {
        this.currenciesService = currenciesService;
    }
    async create(createCurrencyDto, req) {
        return this.currenciesService.create(createCurrencyDto, req.user.sub);
    }
    async findAll() {
        return this.currenciesService.findAll();
    }
    async findOne(id) {
        return this.currenciesService.findOne(id);
    }
    async update(id, updateCurrencyDto, req) {
        return this.currenciesService.update(id, updateCurrencyDto, req.user.sub);
    }
    async remove(id, req) {
        return this.currenciesService.remove(id, req.user.sub);
    }
};
exports.CurrenciesController = CurrenciesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new currency (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Currency created successfully', type: dto_1.CurrencyResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Currency with code already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateCurrencyDto, Object]),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all currencies (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of currencies', type: [dto_1.CurrencyResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get currency by ID (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Currency details', type: dto_1.CurrencyResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update currency (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Currency updated successfully', type: dto_1.CurrencyResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Currency with code already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateCurrencyDto, Object]),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete currency (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Currency deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Currency not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot delete currency that is being used' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], CurrenciesController.prototype, "remove", null);
exports.CurrenciesController = CurrenciesController = __decorate([
    (0, swagger_1.ApiTags)('currencies'),
    (0, common_1.Controller)('currencies'),
    __metadata("design:paramtypes", [currencies_service_1.CurrenciesService])
], CurrenciesController);
//# sourceMappingURL=currencies.controller.js.map