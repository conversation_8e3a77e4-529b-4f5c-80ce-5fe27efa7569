"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/page",{

/***/ "(app-pages-browser)/./app/dashboard/partner/applications/page.tsx":
/*!*****************************************************!*\
  !*** ./app/dashboard/partner/applications/page.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PartnerApplicationsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Clock,Eye,FileEdit,FileText,Play,Plus,Search,Send,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/services/mou-application.service */ \"(app-pages-browser)/./lib/services/mou-application.service.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst getStatusIcon = (status)=>{\n    switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n        case 'draft':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 41,\n                columnNumber: 14\n            }, undefined);\n        case 'pending':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 14\n            }, undefined);\n        case 'approved':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 14\n            }, undefined);\n        case 'rejected':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 14\n            }, undefined);\n        default:\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 49,\n                columnNumber: 14\n            }, undefined);\n    }\n};\nconst getStatusColor = (status)=>{\n    switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n        case 'draft':\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n        case 'pending':\n            return 'bg-amber-100 text-amber-800 border-amber-200';\n        case 'approved':\n            return 'bg-green-100 text-green-800 border-green-200';\n        case 'rejected':\n            return 'bg-red-100 text-red-800 border-red-200';\n        default:\n            return 'bg-gray-100 text-gray-800 border-gray-200';\n    }\n};\nfunction PartnerApplicationsPage() {\n    _s();\n    const { user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [applications, setApplications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PartnerApplicationsPage.useEffect\": ()=>{\n            loadApplications();\n        }\n    }[\"PartnerApplicationsPage.useEffect\"], []);\n    const loadApplications = async ()=>{\n        try {\n            setLoading(true);\n            const data = await _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_10__.mouApplicationService.getMyApplications();\n            setApplications(data);\n        } catch (err) {\n            console.error(\"Failed to load applications:\", err);\n            setError(\"Failed to load applications\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDelete = async (application)=>{\n        if (confirm(\"Are you sure you want to delete application \".concat(application.mouApplicationId, \"?\"))) {\n            try {\n                await _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_10__.mouApplicationService.deleteApplication(application.id);\n                setSuccess(\"Application deleted successfully\");\n                await loadApplications();\n            } catch (err) {\n                var _err_response_data, _err_response;\n                setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Failed to delete application\");\n            }\n        }\n    };\n    const handleSubmit = async (application)=>{\n        if (confirm(\"Are you sure you want to submit application \".concat(application.mouApplicationId, \" for review?\"))) {\n            try {\n                await _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_10__.mouApplicationService.submitApplication(application.id);\n                setSuccess(\"Application submitted for review successfully\");\n                await loadApplications();\n            } catch (err) {\n                var _err_response_data, _err_response;\n                setError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Failed to submit application\");\n            }\n        }\n    };\n    const filteredApplications = applications.filter((app)=>{\n        var _app_responsibility, _app_mou_party_partyName, _app_mou_party, _app_mou;\n        return app.mouApplicationId.toLowerCase().includes(searchTerm.toLowerCase()) || ((_app_responsibility = app.responsibility) === null || _app_responsibility === void 0 ? void 0 : _app_responsibility.toLowerCase().includes(searchTerm.toLowerCase())) || ((_app_mou = app.mou) === null || _app_mou === void 0 ? void 0 : (_app_mou_party = _app_mou.party) === null || _app_mou_party === void 0 ? void 0 : (_app_mou_party_partyName = _app_mou_party.partyName) === null || _app_mou_party_partyName === void 0 ? void 0 : _app_mou_party_partyName.toLowerCase().includes(searchTerm.toLowerCase()));\n    });\n    const getApplicationStatus = (app)=>{\n        // Use the new status field if available\n        if (app.status) {\n            return app.status.toLowerCase();\n        }\n        // Fallback to old logic for backward compatibility\n        if (!app.approvalSteps || app.approvalSteps.length === 0) {\n            return 'draft';\n        }\n        const latestStep = app.approvalSteps.sort((a, b)=>b.stepNumber - a.stepNumber)[0];\n        return latestStep.status.toLowerCase();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight text-cyan-900\",\n                                children: \"MoU Applications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Manage your organization's MoU applications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        asChild: true,\n                        className: \"bg-cyan-600 hover:bg-cyan-700\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/dashboard/partner/applications/new\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                \"New Application\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                className: \"bg-green-50 text-green-700 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                    children: success\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Your Applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"View and manage all your MoU applications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"Search applications...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"pl-8 w-64\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-cyan-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this) : filteredApplications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold mb-2\",\n                                    children: \"No applications found\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: searchTerm ? \"No applications match your search criteria.\" : \"You haven't created any MoU applications yet.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    asChild: true,\n                                    className: \"bg-cyan-600 hover:bg-cyan-700\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        href: \"/dashboard/partner/applications/new\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Create Your First Application\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Application ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"MoU Party\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    children: \"Created\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableHead, {\n                                                    className: \"w-[140px]\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableBody, {\n                                        children: filteredApplications.map((application)=>{\n                                            var _application_mou_party, _application_mou;\n                                            const status = getApplicationStatus(application);\n                                            const completionPercentage = application.completionPercentage || _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_10__.mouApplicationService.calculateCompletionPercentage(application);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: application.mouApplicationId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: ((_application_mou = application.mou) === null || _application_mou === void 0 ? void 0 : (_application_mou_party = _application_mou.party) === null || _application_mou_party === void 0 ? void 0 : _application_mou_party.partyName) || 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                            className: getStatusColor(status),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    getStatusIcon(status),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"capitalize\",\n                                                                        children: status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 bg-gray-200 rounded-full h-2 min-w-[60px]\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-cyan-600 h-2 rounded-full transition-all duration-300\",\n                                                                            style: {\n                                                                                width: \"\".concat(completionPercentage, \"%\")\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                            lineNumber: 244,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground min-w-[35px]\",\n                                                                        children: [\n                                                                            completionPercentage,\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 242,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            status === 'draft' && application.currentStep && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                children: [\n                                                                    \"Step \",\n                                                                    application.currentStep,\n                                                                    \" of 5\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_19__.format)(new Date(application.createdAt), 'MMM dd, yyyy')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_8__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    asChild: true,\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    title: \"View\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                                                        href: \"/dashboard/partner/applications/\".concat(application.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                        lineNumber: 265,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                status === 'draft' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            asChild: true,\n                                                                            size: \"sm\",\n                                                                            variant: \"ghost\",\n                                                                            title: \"Continue\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                                                                href: \"/dashboard/partner/applications/\".concat(application.id, \"/edit\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"ghost\",\n                                                                            onClick: ()=>handleSubmit(application),\n                                                                            title: \"Submit\",\n                                                                            disabled: completionPercentage < 100,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            size: \"sm\",\n                                                                            variant: \"ghost\",\n                                                                            onClick: ()=>handleDelete(application),\n                                                                            className: \"text-red-600 hover:text-red-700\",\n                                                                            title: \"Delete\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Clock_Eye_FileEdit_FileText_Play_Plus_Search_Send_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                                lineNumber: 292,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                                            lineNumber: 285,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, application.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 23\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\app\\\\dashboard\\\\partner\\\\applications\\\\page.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_s(PartnerApplicationsPage, \"gK0wdmfICj+dIwXdkOIfEuLgkmY=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = PartnerApplicationsPage;\nvar _c;\n$RefreshReg$(_c, \"PartnerApplicationsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/partner/applications/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Play)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Play\", [\n    [\n        \"polygon\",\n        {\n            points: \"6 3 20 12 6 21 6 3\",\n            key: \"1oa8hb\"\n        }\n    ]\n]);\n //# sourceMappingURL=play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9wbGF5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sYUFBTyxnRUFBZ0IsQ0FBQyxNQUFRO0lBQ3BDO1FBQUMsU0FBVztRQUFBO1lBQUUsUUFBUSxDQUFzQjtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7Q0FDNUQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcTEVOT1ZPXFxEZXNrdG9wXFxDT0RFXFxXT1JLXFxzcmNcXGljb25zXFxwbGF5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgUGxheVxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0c5c2VXZHZiaUJ3YjJsdWRITTlJallnTXlBeU1DQXhNaUEySURJeElEWWdNeUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvcGxheVxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IFBsYXkgPSBjcmVhdGVMdWNpZGVJY29uKCdQbGF5JywgW1xuICBbJ3BvbHlnb24nLCB7IHBvaW50czogJzYgMyAyMCAxMiA2IDIxIDYgMycsIGtleTogJzFvYThoYicgfV0sXG5dKTtcblxuZXhwb3J0IGRlZmF1bHQgUGxheTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ })

});