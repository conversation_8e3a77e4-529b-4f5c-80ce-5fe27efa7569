"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Building, Calendar, Info } from "lucide-react"
import { Step1Data, MouApplication } from "@/lib/services/mou-application.service"

interface Step1MouInformationProps {
  data: Step1Data
  onChange: (data: Step1Data) => void
  application: MouApplication | null
}

export function Step1MouInformation({ data, onChange, application }: Step1MouInformationProps) {
  const [error, setError] = useState("")

  const handleDurationChange = (value: string) => {
    const years = parseInt(value)
    if (years >= 1 && years <= 10) {
      onChange({
        ...data,
        mouDurationYears: years
      })
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-cyan-900 mb-2">MoU Information</h3>
        <p className="text-muted-foreground">
          Select the Memorandum of Understanding you want to apply for and set the duration.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        {/* Organization Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Building className="h-4 w-4 text-cyan-600" />
              Organization Information
            </CardTitle>
            <CardDescription>
              Your organization details (read-only)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="organizationName">Organization Name</Label>
              <Input
                id="organizationName"
                value={data.organizationName}
                readOnly
                className="bg-gray-50"
              />
              <p className="text-xs text-muted-foreground">
                This information is automatically populated from your account
              </p>
            </div>
          </CardContent>
        </Card>

        {/* MoU Duration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Calendar className="h-4 w-4 text-cyan-600" />
              MoU Duration
            </CardTitle>
            <CardDescription>
              Set the duration for this MoU agreement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mouDurationYears">Duration (Years) *</Label>
              <Select
                value={data.mouDurationYears.toString()}
                onValueChange={handleDurationChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => i + 1).map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year} {year === 1 ? 'Year' : 'Years'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                Duration must be between 1 and 10 years
              </p>
            </div>
          </CardContent>
        </Card>
      </div>



      {/* Validation Summary */}
      <Card className="bg-cyan-50 border-cyan-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-cyan-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-cyan-900">Step 1 Requirements</h4>
              <ul className="text-sm text-cyan-800 mt-2 space-y-1">
                <li className={`flex items-center gap-2 ${data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? 'text-green-700' : ''}`}>
                  {data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? '✓' : '○'} Set MoU duration (1-10 years)
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
