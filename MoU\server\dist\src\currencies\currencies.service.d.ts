import { PrismaService } from '../prisma/prisma.service';
import { CreateCurrencyDto, UpdateCurrencyDto } from './dto';
export declare class CurrenciesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    create(createCurrencyDto: CreateCurrencyDto, currentUserId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    update(id: number, updateCurrencyDto: UpdateCurrencyDto, currentUserId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    remove(id: number, currentUserId: string): Promise<{
        message: string;
    }>;
}
