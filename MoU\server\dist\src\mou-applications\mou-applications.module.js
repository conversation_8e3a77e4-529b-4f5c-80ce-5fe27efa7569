"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationsModule = void 0;
const common_1 = require("@nestjs/common");
const mou_applications_controller_1 = require("./mou-applications.controller");
const mou_applications_service_1 = require("./mou-applications.service");
const platform_express_1 = require("@nestjs/platform-express");
const multer_1 = require("multer");
const path_1 = require("path");
const uuid_1 = require("uuid");
let MouApplicationsModule = class MouApplicationsModule {
};
exports.MouApplicationsModule = MouApplicationsModule;
exports.MouApplicationsModule = MouApplicationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            platform_express_1.MulterModule.register({
                storage: (0, multer_1.diskStorage)({
                    destination: './uploads/documents',
                    filename: (req, file, callback) => {
                        const uniqueSuffix = (0, uuid_1.v4)();
                        const ext = (0, path_1.extname)(file.originalname);
                        callback(null, `${uniqueSuffix}${ext}`);
                    },
                }),
                fileFilter: (req, file, callback) => {
                    const allowedMimes = [
                        'application/pdf',
                        'application/msword',
                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        'application/vnd.ms-excel',
                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        'image/jpeg',
                        'image/png',
                        'image/gif'
                    ];
                    if (allowedMimes.includes(file.mimetype)) {
                        callback(null, true);
                    }
                    else {
                        callback(new Error('Invalid file type'), false);
                    }
                },
                limits: {
                    fileSize: 10 * 1024 * 1024,
                },
            }),
        ],
        controllers: [mou_applications_controller_1.MouApplicationsController],
        providers: [mou_applications_service_1.MouApplicationsService],
        exports: [mou_applications_service_1.MouApplicationsService],
    })
], MouApplicationsModule);
//# sourceMappingURL=mou-applications.module.js.map