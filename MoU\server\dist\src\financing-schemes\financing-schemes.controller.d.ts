import { FinancingSchemesService } from './financing-schemes.service';
import { CreateFinancingSchemeDto, UpdateFinancingSchemeDto } from './dto';
export declare class FinancingSchemesController {
    private readonly financingSchemesService;
    constructor(financingSchemesService: FinancingSchemesService);
    create(createFinancingSchemeDto: CreateFinancingSchemeDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    findAll(): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    update(id: number, updateFinancingSchemeDto: UpdateFinancingSchemeDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
    remove(id: number, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        schemeName: string;
        terms: string | null;
        conditions: string | null;
    }>;
}
