import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsDecimal } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProjectActivityDto {
  @ApiProperty({
    description: 'Unique activity identifier',
    example: 'ACT-2024-001'
  })
  @IsString()
  @IsNotEmpty()
  activityId: string;

  @ApiProperty({
    description: 'Activity name',
    example: 'Community Health Training'
  })
  @IsString()
  @IsNotEmpty()
  activityName: string;

  @ApiProperty({
    description: 'Activity description',
    required: false,
    example: 'Training local health workers in basic medical procedures'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Activity timeline',
    required: false,
    example: 'Q1 2024 - Q2 2024'
  })
  @IsOptional()
  @IsString()
  timeline?: string;

  @ApiProperty({
    description: 'Budget allocation for this activity',
    required: false,
    example: 50000.00
  })
  @IsOptional()
  @Type(() => Number)
  budgetAllocation?: number;

  @ApiProperty({
    description: 'Currency code',
    required: false,
    default: 'USD',
    example: 'USD'
  })
  @IsOptional()
  @IsString()
  currency?: string;
}
