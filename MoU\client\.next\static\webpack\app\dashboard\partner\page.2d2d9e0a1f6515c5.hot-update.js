"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/page",{

/***/ "(app-pages-browser)/./lib/services/mou-application.service.ts":
/*!*************************************************!*\
  !*** ./lib/services/mou-application.service.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mouApplicationService: () => (/* binding */ mouApplicationService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./lib/api.ts\");\n\nconst mouApplicationService = {\n    // Get all MoU applications for the current user (partner)\n    async getMyApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/my-applications\");\n        return response.data;\n    },\n    // Get all MoU applications (admin/reviewer access)\n    async getAllApplications () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications\");\n        return response.data;\n    },\n    // Get a specific MoU application by ID\n    async getApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Create a new MoU application (draft by default)\n    async createApplication (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications\", {\n            ...data,\n            status: data.status || 'DRAFT',\n            currentStep: data.currentStep || 1\n        });\n        return response.data;\n    },\n    // Create a draft application with minimal data\n    async createDraft (mouId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/draft\", {\n            mouId,\n            status: 'DRAFT',\n            currentStep: 1,\n            completionPercentage: 0\n        });\n        return response.data;\n    },\n    // Update an existing MoU application\n    async updateApplication (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/mou-applications/\".concat(id), data);\n        return response.data;\n    },\n    // Update a specific step of the application\n    async updateStep (id, stepNumber, data) {\n        let autoSave = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/mou-applications/\".concat(id, \"/step/\").concat(stepNumber), {\n            data,\n            autoSave,\n            lastAutoSave: autoSave ? new Date().toISOString() : undefined\n        });\n        return response.data;\n    },\n    // Auto-save application data\n    async autoSave (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(id, \"/auto-save\"), {\n            ...data,\n            lastAutoSave: new Date().toISOString()\n        });\n        return response.data;\n    },\n    // Delete a MoU application\n    async deleteApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(id));\n        return response.data;\n    },\n    // Submit application for review (convert from DRAFT to SUBMITTED)\n    async submitApplication (id) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(id, \"/submit\"));\n        return response.data;\n    },\n    // Get available MoUs for application\n    async getAvailableMous () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mous/available\");\n        return response.data;\n    },\n    // Get application statistics for dashboard\n    async getApplicationStats () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/mou-applications/stats\");\n        return response.data;\n    },\n    // Document upload methods\n    async uploadDocument (applicationId, file, documentType) {\n        let isRequired = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        const formData = new FormData();\n        formData.append('file', file);\n        formData.append('documentType', documentType);\n        formData.append('isRequired', isRequired.toString());\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/documents\"), formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    async deleteDocument (applicationId, documentId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(applicationId, \"/documents/\").concat(documentId));\n        return response.data;\n    },\n    // Responsibility management\n    async addResponsibility (applicationId, responsibilityText, order) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/responsibilities\"), {\n            responsibilityText,\n            order\n        });\n        return response.data;\n    },\n    async updateResponsibility (applicationId, responsibilityId, responsibilityText) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/mou-applications/\".concat(applicationId, \"/responsibilities/\").concat(responsibilityId), {\n            responsibilityText\n        });\n        return response.data;\n    },\n    async deleteResponsibility (applicationId, responsibilityId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/mou-applications/\".concat(applicationId, \"/responsibilities/\").concat(responsibilityId));\n        return response.data;\n    },\n    // Project activity management\n    async addProjectActivity (projectId, activity) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/projects/\".concat(projectId, \"/activities\"), activity);\n        return response.data;\n    },\n    async updateProjectActivity (projectId, activityId, activity) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/projects/\".concat(projectId, \"/activities/\").concat(activityId), activity);\n        return response.data;\n    },\n    async deleteProjectActivity (projectId, activityId) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/projects/\".concat(projectId, \"/activities/\").concat(activityId));\n        return response.data;\n    },\n    // Validation methods\n    async validateStep (applicationId, stepNumber) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/mou-applications/\".concat(applicationId, \"/validate-step/\").concat(stepNumber));\n        return response.data;\n    },\n    // Progress calculation\n    calculateCompletionPercentage (application) {\n        let completedSteps = 0;\n        const totalSteps = 5;\n        // Step 1: MoU Information\n        if (application.mouId && application.mouDurationYears) {\n            completedSteps++;\n        }\n        // Step 2: Party Details\n        if (application.partyName && application.signatoryName && application.signatoryPosition && application.responsibilities && application.responsibilities.length > 0) {\n            completedSteps++;\n        }\n        // Step 3: Project Information\n        if (application.projects && application.projects.length > 0) {\n            const hasValidProjects = application.projects.some((p)=>p.projectName && p.startDate && p.endDate && p.totalBudget);\n            if (hasValidProjects) {\n                completedSteps++;\n            }\n        }\n        // Step 4: Document Upload\n        if (application.documents && application.documents.length > 0) {\n            const hasRequiredDocs = application.documents.some((d)=>d.isRequired);\n            if (hasRequiredDocs) {\n                completedSteps++;\n            }\n        }\n        // Step 5: Review & Submit (completed when status is not DRAFT)\n        if (application.status !== 'DRAFT') {\n            completedSteps++;\n        }\n        return Math.round(completedSteps / totalSteps * 100);\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/mou-application.service.ts\n"));

/***/ })

});