"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const organization_types_service_1 = require("./organization-types.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
const public_decorator_1 = require("../auth/decorator/public.decorator");
let OrganizationTypesController = class OrganizationTypesController {
    constructor(organizationTypesService) {
        this.organizationTypesService = organizationTypesService;
    }
    async create(createOrganizationTypeDto, req) {
        return this.organizationTypesService.create(createOrganizationTypeDto, req.user.sub);
    }
    async findAll() {
        return this.organizationTypesService.findAll();
    }
    async findOne(id) {
        return this.organizationTypesService.findOne(id);
    }
    async update(id, updateOrganizationTypeDto, req) {
        return this.organizationTypesService.update(id, updateOrganizationTypeDto, req.user.sub);
    }
    async remove(id, req) {
        return this.organizationTypesService.remove(id, req.user.sub);
    }
};
exports.OrganizationTypesController = OrganizationTypesController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new organization type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Organization type created successfully', type: dto_1.OrganizationTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Organization type with name already exists' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateOrganizationTypeDto, Object]),
    __metadata("design:returntype", Promise)
], OrganizationTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all organization types (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of organization types', type: [dto_1.OrganizationTypeResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrganizationTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get organization type by ID (Public access)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization type details', type: dto_1.OrganizationTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Organization type not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], OrganizationTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update organization type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization type updated successfully', type: dto_1.OrganizationTypeResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Organization type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Organization type with name already exists' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, dto_1.UpdateOrganizationTypeDto, Object]),
    __metadata("design:returntype", Promise)
], OrganizationTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete organization type (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Organization type deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Organization type not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot delete organization type that is being used' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], OrganizationTypesController.prototype, "remove", null);
exports.OrganizationTypesController = OrganizationTypesController = __decorate([
    (0, swagger_1.ApiTags)('organization-types'),
    (0, common_1.Controller)('organization-types'),
    __metadata("design:paramtypes", [organization_types_service_1.OrganizationTypesService])
], OrganizationTypesController);
//# sourceMappingURL=organization-types.controller.js.map