import { PrismaService } from '../prisma/prisma.service';
import { CreateMouDto, UpdateMouDto } from './dto';
export declare class MouService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createMouDto: CreateMouDto, userId: string): Promise<{
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    findAll(userId: string): Promise<({
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    })[]>;
    findOne(id: string, userId: string): Promise<{
        party: {
            id: string;
            name: string;
            organizationId: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    update(id: string, updateMouDto: UpdateMouDto, userId: string): Promise<{
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    remove(id: string, userId: string): Promise<{
        message: string;
    }>;
}
