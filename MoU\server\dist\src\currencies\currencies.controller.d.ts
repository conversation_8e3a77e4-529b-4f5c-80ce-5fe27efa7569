import { CurrenciesService } from './currencies.service';
import { CreateCurrencyDto, UpdateCurrencyDto } from './dto';
export declare class CurrenciesController {
    private readonly currenciesService;
    constructor(currenciesService: CurrenciesService);
    create(createCurrencyDto: CreateCurrencyDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    findAll(): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    update(id: number, updateCurrencyDto: UpdateCurrencyDto, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currencyCode: string;
        currencyName: string;
    }>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
}
