"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let MouService = class MouService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createMouDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user || user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only administrators can create MoUs');
        }
        const existingMou = await this.prisma.mou.findUnique({
            where: { mouId: createMouDto.mouId }
        });
        if (existingMou) {
            throw new common_1.ConflictException('MoU with this ID already exists');
        }
        const party = await this.prisma.party.findUnique({
            where: { id: createMouDto.partyId }
        });
        if (!party) {
            throw new common_1.NotFoundException('Party not found');
        }
        return this.prisma.mou.create({
            data: createMouDto,
            include: {
                party: {
                    select: {
                        id: true,
                        name: true,
                        signatory: true,
                        position: true,
                    }
                }
            }
        });
    }
    async findAll(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const whereClause = user.role === 'ADMIN'
            ? { deleted: false }
            : {
                deleted: false,
                party: {
                    organizationId: user.organizationId
                }
            };
        return this.prisma.mou.findMany({
            where: whereClause,
            include: {
                party: {
                    select: {
                        id: true,
                        name: true,
                        signatory: true,
                        position: true,
                    }
                }
            },
            orderBy: { createAt: 'desc' }
        });
    }
    async findOne(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const mou = await this.prisma.mou.findUnique({
            where: { id, deleted: false },
            include: {
                party: {
                    select: {
                        id: true,
                        name: true,
                        signatory: true,
                        position: true,
                        organizationId: true,
                    }
                }
            }
        });
        if (!mou) {
            throw new common_1.NotFoundException('MoU not found');
        }
        if (user.role !== 'ADMIN' && mou.party.organizationId !== user.organizationId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return mou;
    }
    async update(id, updateMouDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const mou = await this.findOne(id, userId);
        if (user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only administrators can update MoUs');
        }
        if (updateMouDto.mouId && updateMouDto.mouId !== mou.mouId) {
            const existingMou = await this.prisma.mou.findUnique({
                where: { mouId: updateMouDto.mouId }
            });
            if (existingMou) {
                throw new common_1.ConflictException('MoU with this ID already exists');
            }
        }
        return this.prisma.mou.update({
            where: { id },
            data: updateMouDto,
            include: {
                party: {
                    select: {
                        id: true,
                        name: true,
                        signatory: true,
                        position: true,
                    }
                }
            }
        });
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (user.role !== 'ADMIN') {
            throw new common_1.ForbiddenException('Only administrators can delete MoUs');
        }
        const mou = await this.findOne(id, userId);
        const activeApplications = await this.prisma.mouApplication.count({
            where: {
                mouId: id,
                status: { in: ['DRAFT', 'SUBMITTED', 'UNDER_REVIEW'] },
                deleted: false
            }
        });
        if (activeApplications > 0) {
            throw new common_1.ConflictException('Cannot delete MoU with active applications');
        }
        await this.prisma.mou.update({
            where: { id },
            data: { deleted: true }
        });
        return { message: 'MoU deleted successfully' };
    }
};
exports.MouService = MouService;
exports.MouService = MouService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MouService);
//# sourceMappingURL=mou.service.js.map