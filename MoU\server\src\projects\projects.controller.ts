import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto, ProjectResponseDto, CreateProjectActivityDto, UpdateProjectActivityDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';

@ApiTags('projects')
@Controller('projects')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class ProjectsController {
  constructor(private readonly projectsService: ProjectsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({ status: 201, description: 'Project created successfully', type: ProjectResponseDto })
  @ApiResponse({ status: 409, description: 'Project with ID already exists' })
  @ApiResponse({ status: 404, description: 'Related entity not found' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  async create(@Body() createProjectDto: CreateProjectDto, @Request() req: any) {
    return this.projectsService.create(createProjectDto, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get all projects (Admin sees all, others see their organization\'s projects)' })
  @ApiResponse({ status: 200, description: 'Returns list of projects', type: [ProjectResponseDto] })
  async findAll(@Request() req: any) {
    return this.projectsService.findAll(req.user.sub);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get project by ID' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Returns project details', type: ProjectResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.projectsService.findOne(id, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a project' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Project updated successfully', type: ProjectResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @ApiResponse({ status: 409, description: 'Project with ID already exists' })
  async update(@Param('id') id: string, @Body() updateProjectDto: UpdateProjectDto, @Request() req: any) {
    return this.projectsService.update(id, updateProjectDto, req.user.sub);
  }

  @Post(':id/activities')
  @ApiOperation({ summary: 'Create project activities with nested structure support' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 201, description: 'Activity created successfully' })
  @ApiResponse({ status: 409, description: 'Activity with ID already exists' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async createActivity(
    @Param('id') id: string, 
    @Body() createProjectActivityDto: CreateProjectActivityDto, 
    @Request() req: any
  ) {
    return this.projectsService.createActivity(id, createProjectActivityDto, req.user.sub);
  }

  @Get(':id/activities')
  @ApiOperation({ summary: 'Get all activities for a project' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Returns list of project activities' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async findActivities(@Param('id') id: string, @Request() req: any) {
    return this.projectsService.findActivities(id, req.user.sub);
  }

  @Get(':id/activities/:activityId')
  @ApiOperation({ summary: 'Get specific activity by ID' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiParam({ name: 'activityId', description: 'Activity ID' })
  @ApiResponse({ status: 200, description: 'Returns activity details' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  async findActivity(
    @Param('id') id: string, 
    @Param('activityId') activityId: string, 
    @Request() req: any
  ) {
    return this.projectsService.findActivity(id, activityId, req.user.sub);
  }

  @Patch(':id/activities/:activityId')
  @ApiOperation({ summary: 'Update a project activity' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiParam({ name: 'activityId', description: 'Activity ID' })
  @ApiResponse({ status: 200, description: 'Activity updated successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  @ApiResponse({ status: 409, description: 'Activity with ID already exists' })
  async updateActivity(
    @Param('id') id: string,
    @Param('activityId') activityId: string,
    @Body() updateProjectActivityDto: UpdateProjectActivityDto,
    @Request() req: any
  ) {
    return this.projectsService.updateActivity(id, activityId, updateProjectActivityDto, req.user.sub);
  }

  @Delete(':id/activities/:activityId')
  @ApiOperation({ summary: 'Delete a project activity' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiParam({ name: 'activityId', description: 'Activity ID' })
  @ApiResponse({ status: 200, description: 'Activity deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Activity not found' })
  async deleteActivity(
    @Param('id') id: string,
    @Param('activityId') activityId: string,
    @Request() req: any
  ) {
    return this.projectsService.deleteActivity(id, activityId, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a project (only from draft applications)' })
  @ApiParam({ name: 'id', description: 'Project ID' })
  @ApiResponse({ status: 200, description: 'Project deleted successfully' })
  @ApiResponse({ status: 400, description: 'Cannot delete project from submitted application' })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.projectsService.remove(id, req.user.sub);
  }
}
