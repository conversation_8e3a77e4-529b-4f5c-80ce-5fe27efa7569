import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, Length } from 'class-validator';

export class CreateCurrencyDto {
    @ApiProperty({ description: 'Currency code (e.g., USD, EUR, RWF)', example: 'USD' })
    @IsNotEmpty()
    @IsString()
    @Length(3, 3, { message: 'Currency code must be exactly 3 characters' })
    currencyCode: string;

    @ApiProperty({ description: 'Currency name (e.g., US Dollar, Euro, Rwandan Franc)', example: 'US Dollar' })
    @IsNotEmpty()
    @IsString()
    currencyName: string;
}

export class UpdateCurrencyDto {
    @ApiProperty({ required: false, description: 'Currency code (e.g., USD, EUR, RWF)', example: 'USD' })
    @IsOptional()
    @IsString()
    @Length(3, 3, { message: 'Currency code must be exactly 3 characters' })
    currencyCode?: string;

    @ApiProperty({ required: false, description: 'Currency name (e.g., US Dollar, Euro, Rwandan Franc)', example: 'US Dollar' })
    @IsOptional()
    @IsString()
    currencyName?: string;
}

export class CurrencyResponseDto {
    @ApiProperty({ description: 'Currency ID', example: 1 })
    id: number;

    @ApiProperty({ description: 'Currency code', example: 'USD' })
    currencyCode: string;

    @ApiProperty({ description: 'Currency name', example: 'US Dollar' })
    currencyName: string;

    @ApiProperty({ description: 'Creation date' })
    createdAt: Date;

    @ApiProperty({ description: 'Last update date' })
    updatedAt: Date;

    @ApiProperty({ description: 'Soft delete flag' })
    deleted: boolean;
}
