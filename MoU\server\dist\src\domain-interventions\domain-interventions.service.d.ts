import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsService {
    private prisma;
    constructor(prisma: PrismaService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: ({
            children: ({
                children: {
                    id: number;
                    updatedAt: Date;
                    deleted: boolean;
                    createdAt: Date;
                    description: string | null;
                    parentId: number | null;
                    domainName: string;
                }[];
            } & {
                id: number;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                description: string | null;
                parentId: number | null;
                domainName: string;
            })[];
        } & {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        })[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        children: any[];
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    remove(id: number, userId: string): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    private checkCircularReference;
}
