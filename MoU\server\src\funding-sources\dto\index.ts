import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';

export class CreateFundingSourceDto {
    @ApiProperty()
    @IsNotEmpty()
    @IsString()
    sourceName: string;
}

export class UpdateFundingSourceDto {
    @ApiProperty({ required: false })
    @IsOptional()
    @IsString()
    sourceName?: string;
}

export class FundingSourceResponseDto {
    @ApiProperty()
    id: number;

    @ApiProperty()
    sourceName: string;

    @ApiProperty()
    createAt: Date;

    @ApiProperty()
    updatedAt: Date;

    @ApiProperty()
    deleted: boolean;
}
