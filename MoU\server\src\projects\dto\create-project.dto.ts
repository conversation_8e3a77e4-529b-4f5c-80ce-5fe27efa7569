import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsUUID, IsOptional, IsInt, IsDateString, IsDecimal, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateProjectDto {
  @ApiProperty({
    description: 'Unique project identifier',
    example: 'PROJ-2024-001'
  })
  @IsString()
  @IsNotEmpty()
  projectId: string;

  @ApiProperty({
    description: 'Project name',
    example: 'Rural Healthcare Initiative'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Project description',
    required: false,
    example: 'Improving healthcare access in rural communities'
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Project duration in months',
    example: 36
  })
  @IsInt()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Project start date',
    required: false,
    example: '2024-01-01'
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    description: 'Project end date',
    required: false,
    example: '2026-12-31'
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    description: 'Total project budget',
    required: false,
    example: 1000000.00
  })
  @IsOptional()
  @Type(() => Number)
  totalBudget?: number;

  @ApiProperty({
    description: 'Currency code',
    required: false,
    default: 'USD',
    example: 'USD'
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiProperty({
    description: 'Budget type ID',
    example: 1
  })
  @IsInt()
  budgetTypeId: number;

  @ApiProperty({
    description: 'Funding unit ID',
    example: 1
  })
  @IsInt()
  fundingUnitId: number;

  @ApiProperty({
    description: 'Funding source ID',
    example: 1
  })
  @IsInt()
  fundingSourceId: number;

  @ApiProperty({
    description: 'Organization ID',
    example: 'uuid-organization-id'
  })
  @IsUUID()
  @IsNotEmpty()
  organizationId: string;

  @ApiProperty({
    description: 'Project document ID',
    example: 'uuid-document-id'
  })
  @IsUUID()
  @IsNotEmpty()
  projectDocumentId: string;

  @ApiProperty({
    description: 'MoU application ID',
    example: 'uuid-application-id'
  })
  @IsUUID()
  @IsNotEmpty()
  mouApplicationId: string;
}
