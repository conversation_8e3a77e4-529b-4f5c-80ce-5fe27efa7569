import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateMouDto, UpdateMouDto } from './dto';

@Injectable()
export class MouService {
  constructor(
    private prisma: PrismaService,
  ) {}

  async create(createMouDto: CreateMouDto, userId: string) {
    // Verify user has admin permissions
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only administrators can create MoUs');
    }

    // Check if MoU ID already exists
    const existingMou = await this.prisma.mou.findUnique({
      where: { mouId: createMouDto.mouId }
    });

    if (existingMou) {
      throw new ConflictException('MoU with this ID already exists');
    }

    // Verify party exists
    const party = await this.prisma.party.findUnique({
      where: { id: createMouDto.partyId }
    });

    if (!party) {
      throw new NotFoundException('Party not found');
    }

    return this.prisma.mou.create({
      data: createMouDto,
      include: {
        party: {
          select: {
            id: true,
            name: true,
            signatory: true,
            position: true,
          }
        }
      }
    });
  }

  async findAll(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    // Admin can see all MoUs, others see only their organization's MoUs
    const whereClause = user.role === 'ADMIN' 
      ? { deleted: false }
      : {
          deleted: false,
          party: {
            organizationId: user.organizationId
          }
        };

    return this.prisma.mou.findMany({
      where: whereClause,
      include: {
        party: {
          select: {
            id: true,
            name: true,
            signatory: true,
            position: true,
          }
        }
      },
      orderBy: { createAt: 'desc' }
    });
  }

  async findOne(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    const mou = await this.prisma.mou.findUnique({
      where: { id, deleted: false },
      include: {
        party: {
          select: {
            id: true,
            name: true,
            signatory: true,
            position: true,
            organizationId: true,
          }
        }
      }
    });

    if (!mou) {
      throw new NotFoundException('MoU not found');
    }

    // Check access permissions
    if (user.role !== 'ADMIN' && mou.party.organizationId !== user.organizationId) {
      throw new ForbiddenException('Access denied');
    }

    return mou;
  }

  async update(id: string, updateMouDto: UpdateMouDto, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    const mou = await this.findOne(id, userId);

    // Only admin can update MoUs
    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only administrators can update MoUs');
    }

    // Check for mouId conflicts if updating mouId
    if (updateMouDto.mouId && updateMouDto.mouId !== mou.mouId) {
      const existingMou = await this.prisma.mou.findUnique({
        where: { mouId: updateMouDto.mouId }
      });

      if (existingMou) {
        throw new ConflictException('MoU with this ID already exists');
      }
    }

    return this.prisma.mou.update({
      where: { id },
      data: updateMouDto,
      include: {
        party: {
          select: {
            id: true,
            name: true,
            signatory: true,
            position: true,
          }
        }
      }
    });
  }

  async remove(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only administrators can delete MoUs');
    }

    const mou = await this.findOne(id, userId);

    // Check if MoU has active applications
    const activeApplications = await this.prisma.mouApplication.count({
      where: {
        mouId: id,
        status: { in: ['DRAFT', 'SUBMITTED', 'UNDER_REVIEW'] },
        deleted: false
      }
    });

    if (activeApplications > 0) {
      throw new ConflictException('Cannot delete MoU with active applications');
    }

    // Soft delete
    await this.prisma.mou.update({
      where: { id },
      data: { deleted: true }
    });

    return { message: 'MoU deleted successfully' };
  }
}
