import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { MouService } from './mou.service';
import { CreateMouDto, UpdateMouDto, MouResponseDto } from './dto';
import { JwtGuard } from '../auth/guard/jwt-auth.guard';

@ApiTags('mou')
@Controller('mou')
@UseGuards(JwtGuard)
@ApiBearerAuth()
export class MouController {
  constructor(private readonly mouService: MouService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new MoU (Admin only)' })
  @ApiResponse({ status: 201, description: 'MoU created successfully', type: MouResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 409, description: 'MoU with ID already exists' })
  @ApiResponse({ status: 404, description: 'Party not found' })
  async create(@Body() createMouDto: CreateMouDto, @Request() req: any) {
    return this.mouService.create(createMouDto, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: 'Get all MoUs (Admin sees all, others see their organization\'s MoUs)' })
  @ApiResponse({ status: 200, description: 'Returns list of MoUs', type: [MouResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  async findAll(@Request() req: any) {
    return this.mouService.findAll(req.user.sub);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get MoU by ID' })
  @ApiParam({ name: 'id', description: 'MoU ID' })
  @ApiResponse({ status: 200, description: 'Returns MoU details', type: MouResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Access denied' })
  @ApiResponse({ status: 404, description: 'MoU not found' })
  async findOne(@Param('id') id: string, @Request() req: any) {
    return this.mouService.findOne(id, req.user.sub);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a MoU (Admin only)' })
  @ApiParam({ name: 'id', description: 'MoU ID' })
  @ApiResponse({ status: 200, description: 'MoU updated successfully', type: MouResponseDto })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'MoU not found' })
  @ApiResponse({ status: 409, description: 'MoU with ID already exists' })
  async update(@Param('id') id: string, @Body() updateMouDto: UpdateMouDto, @Request() req: any) {
    return this.mouService.update(id, updateMouDto, req.user.sub);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a MoU (Admin only)' })
  @ApiParam({ name: 'id', description: 'MoU ID' })
  @ApiResponse({ status: 200, description: 'MoU deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 404, description: 'MoU not found' })
  @ApiResponse({ status: 409, description: 'Cannot delete MoU with active applications' })
  async remove(@Param('id') id: string, @Request() req: any) {
    return this.mouService.remove(id, req.user.sub);
  }
}
