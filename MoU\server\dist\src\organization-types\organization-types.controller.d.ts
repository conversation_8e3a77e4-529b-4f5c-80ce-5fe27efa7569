import { OrganizationTypesService } from './organization-types.service';
import { CreateOrganizationTypeDto, UpdateOrganizationTypeDto } from './dto';
export declare class OrganizationTypesController {
    private readonly organizationTypesService;
    constructor(organizationTypesService: OrganizationTypesService);
    create(createOrganizationTypeDto: CreateOrganizationTypeDto, req: any): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    findAll(): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    update(id: number, updateOrganizationTypeDto: UpdateOrganizationTypeDto, req: any): Promise<{
        id: number;
        typeName: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
    }>;
    remove(id: number, req: any): Promise<{
        message: string;
    }>;
}
