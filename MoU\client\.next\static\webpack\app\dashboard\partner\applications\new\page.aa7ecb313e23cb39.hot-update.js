"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/step5-review-submit.tsx":
/*!*********************************************************!*\
  !*** ./components/wizard-steps/step5-review-submit.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Step5ReviewSubmit: () => (/* binding */ Step5ReviewSubmit)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,Calendar,CheckCircle,Edit,File,FileText,Info,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ Step5ReviewSubmit auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Step5ReviewSubmit(param) {\n    let { data, onChange, application, allStepData, onSubmit } = param;\n    _s();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTermsChange = (checked)=>{\n        onChange({\n            ...data,\n            termsAccepted: checked\n        });\n    };\n    const handleFinalReviewChange = (checked)=>{\n        onChange({\n            ...data,\n            finalReview: checked\n        });\n    };\n    const handleSubmit = async ()=>{\n        if (!data.termsAccepted || !data.finalReview) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            await onSubmit();\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const formatCurrency = function(amount) {\n        let currency = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"USD\";\n        if (!amount) return \"Not specified\";\n        return \"\".concat(amount.toLocaleString(), \" \").concat(currency);\n    };\n    const getTotalProjectBudget = ()=>{\n        return allStepData.step3.projects.reduce((total, project)=>{\n            return total + (project.totalBudget || 0);\n        }, 0);\n    };\n    const getValidationIssues = ()=>{\n        const issues = [];\n        // Step 1 validation\n        if (!allStepData.step1.mouDurationYears || allStepData.step1.mouDurationYears < 1) {\n            issues.push(\"Valid MoU duration is required\");\n        }\n        // Step 2 validation\n        if (!allStepData.step2.partyName.trim()) {\n            issues.push(\"Party name is required\");\n        }\n        if (!allStepData.step2.signatoryName.trim()) {\n            issues.push(\"Signatory name is required\");\n        }\n        if (!allStepData.step2.signatoryPosition.trim()) {\n            issues.push(\"Signatory position is required\");\n        }\n        if (!allStepData.step2.responsibilities.some((r)=>r.trim())) {\n            issues.push(\"At least one responsibility is required\");\n        }\n        // Step 3 validation\n        if (allStepData.step3.projects.length === 0) {\n            issues.push(\"At least one project is required\");\n        }\n        const hasValidProjects = allStepData.step3.projects.some((p)=>p.projectName.trim() && p.startDate && p.endDate && p.totalBudget);\n        if (!hasValidProjects) {\n            issues.push(\"Complete project information is required\");\n        }\n        return issues;\n    };\n    const validationIssues = getValidationIssues();\n    const canSubmit = validationIssues.length === 0 && data.termsAccepted && data.finalReview;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-cyan-900 mb-2\",\n                        children: \"Review & Submit\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Review all information before submitting your MoU application. Once submitted, changes will require approval.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            validationIssues.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium mb-2\",\n                                children: \"Please resolve the following issues before submitting:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside space-y-1\",\n                                children: validationIssues.map((issue, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: issue\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-cyan-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"MoU Information\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 md:grid-cols-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: \"Organization\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: allStepData.step1.organizationName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            className: \"text-sm font-medium text-muted-foreground\",\n                                            children: \"MoU Duration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                allStepData.step1.mouDurationYears,\n                                                \" years\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5 text-cyan-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Party Details\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4 md:grid-cols-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Party Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: allStepData.step2.partyName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                className: \"text-sm font-medium text-muted-foreground\",\n                                                children: \"Signatory\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: allStepData.step2.signatoryName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: allStepData.step2.signatoryPosition\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-sm font-medium text-muted-foreground\",\n                                        children: \"Responsibilities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 space-y-2\",\n                                        children: allStepData.step2.responsibilities.filter((r)=>r.trim()).map((responsibility, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-gray-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm\",\n                                                    children: responsibility\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 text-cyan-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Projects (\",\n                                        allStepData.step3.projects.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-cyan-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-cyan-900\",\n                                        children: \"Total Budget\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-cyan-900\",\n                                        children: [\n                                            \"$\",\n                                            getTotalProjectBudget().toLocaleString(),\n                                            \" USD\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this),\n                            allStepData.step3.projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border rounded-lg p-4 space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium\",\n                                                    children: project.projectName || \"Project \".concat(index + 1)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        project.activities.length,\n                                                        \" activit\",\n                                                        project.activities.length !== 1 ? 'ies' : 'y'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this),\n                                        project.projectDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: project.projectDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-2 md:grid-cols-3 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Duration\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: [\n                                                                project.startDate,\n                                                                \" to \",\n                                                                project.endDate\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Budget\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: formatCurrency(project.totalBudget, project.currency)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Currency\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: project.currency\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        project.activities.filter((a)=>a.activityName.trim()).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Activities\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 space-y-1\",\n                                                    children: project.activities.filter((a)=>a.activityName.trim()).map((activity, actIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm p-2 bg-gray-50 rounded\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: activity.activityName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                activity.budgetAllocation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-muted-foreground\",\n                                                                    children: [\n                                                                        \"(\",\n                                                                        formatCurrency(activity.budgetAllocation, activity.currency),\n                                                                        \")\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, actIndex, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-5 w-5 text-cyan-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Documents\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Edit\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: (application === null || application === void 0 ? void 0 : application.documents) && application.documents.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: application.documents.map((doc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"h-4 w-4 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: doc.originalName\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: doc.documentType\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, this),\n                                        doc.isRequired && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                            variant: \"destructive\",\n                                            className: \"text-xs\",\n                                            children: \"Required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground text-center py-4\",\n                            children: \"No documents uploaded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5 text-cyan-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Final Confirmation\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Please confirm the following before submitting your application\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                        id: \"finalReview\",\n                                        checked: data.finalReview,\n                                        onCheckedChange: handleFinalReviewChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                htmlFor: \"finalReview\",\n                                                className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                children: \"I have reviewed all information and confirm it is accurate and complete\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Please ensure all details are correct as changes after submission will require approval\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                        id: \"terms\",\n                                        checked: data.termsAccepted,\n                                        onCheckedChange: handleTermsChange\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                htmlFor: \"terms\",\n                                                className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                children: \"I accept the terms and conditions of the MoU application process\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"By submitting this application, you agree to the Ministry of Health's terms and conditions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-green-50 border-green-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-8 w-8 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-green-900\",\n                                        children: \"Ready to Submit\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-800 mt-1\",\n                                        children: \"Your application will be submitted to the Ministry of Health for review\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleSubmit,\n                                disabled: !canSubmit || isSubmitting,\n                                size: \"lg\",\n                                className: \"bg-green-600 hover:bg-green-700\",\n                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Submitting...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Submit Application\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this),\n                            !canSubmit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                                className: \"bg-amber-50 border-amber-200 text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_Calendar_CheckCircle_Edit_File_FileText_Info_Send_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                                        className: \"text-amber-800\",\n                                        children: \"Please complete all required fields and accept the terms and conditions to submit your application.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n                lineNumber: 386,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step5-review-submit.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(Step5ReviewSubmit, \"oafqrj090+oRf5bsyDQJHsshgoc=\");\n_c = Step5ReviewSubmit;\nvar _c;\n$RefreshReg$(_c, \"Step5ReviewSubmit\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/step5-review-submit.tsx\n"));

/***/ })

});