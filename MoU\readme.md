# MOH MoU

This folder contains a **NestJS backend** (`/server`) and a **Next.js frontend**. Follow the instructions below to set up and run both applications locally.

---

## 🛠️ Prerequisites

Ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v16 or above recommended)
- [npm](https://www.npmjs.com/)
- [PostgreSQL](https://www.postgresql.org/) or another database supported by Prisma

---

## 📦 Backend - NestJS (`/server`)

### 1. Navigate to the server directory:

```bash
cd server
````

### 2. Configure Environment Variables

Edit `.env` and set the DB URL, JWT secret, and other necessary variables including admin credentials:


### 3. Install Dependencies

```bash
npm install
```

### 4. Set Up the Database

Push the Prisma schema to your database:

```bash
npx prisma db push
```

You can also run migrations if your project uses them:

```bash
npx prisma migrate dev
```

Seed the database with initial data :

```bash
npm run seed
```

### 5. Start the Development Server

```bash
npm run dev
```

---

## 🌐 Frontend - Next.js

### 1. Navigate to the root or frontend directory

If the frontend is in the root folder:

```bash
cd ..
```

Or if it's in a subfolder like `/client`, use:

```bash
cd client
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Start the Development Server

```bash
npm run dev
```

---

## ✅ Project Structure

```
/server        -> NestJS backend (API)
/client or /   -> Next.js frontend (UI)
```
---

Happy coding! 🚀

