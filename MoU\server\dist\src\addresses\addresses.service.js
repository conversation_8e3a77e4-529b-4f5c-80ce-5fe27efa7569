"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AddressesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const dto_1 = require("./dto");
let AddressesService = AddressesService_1 = class AddressesService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(AddressesService_1.name);
    }
    async findByOrganization(organizationId) {
        try {
            const addresses = await this.prisma.address.findMany({
                where: {
                    organizationId,
                    deleted: false
                },
                orderBy: { createdAt: 'desc' }
            });
            return addresses;
        }
        catch (error) {
            this.logger.error(`Failed to fetch addresses for organization ${organizationId}`, error.stack);
            throw new Error('Failed to fetch addresses');
        }
    }
    async findOne(id) {
        try {
            const address = await this.prisma.address.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!address) {
                throw new common_1.NotFoundException('Address not found');
            }
            return address;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch address ${id}`, error.stack);
            throw new Error('Failed to fetch address');
        }
    }
    async create(createAddressDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const organization = await this.prisma.organization.findUnique({
                where: { id: createAddressDto.organizationId }
            });
            if (!organization) {
                throw new common_1.NotFoundException('Organization not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== createAddressDto.organizationId) {
                throw new common_1.ForbiddenException('You can only create addresses for your own organization');
            }
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    organizationId: createAddressDto.organizationId,
                    addressType: createAddressDto.addressType,
                    deleted: false
                }
            });
            if (existingAddress) {
                throw new common_1.ConflictException(`Organization already has a ${createAddressDto.addressType} address`);
            }
            const addressCount = await this.prisma.address.count({
                where: {
                    organizationId: createAddressDto.organizationId,
                    deleted: false
                }
            });
            if (addressCount >= 2) {
                throw new common_1.BadRequestException('Organization cannot have more than 2 addresses');
            }
            if (createAddressDto.addressType === dto_1.AddressType.RWANDA) {
                if (!createAddressDto.province || !createAddressDto.district) {
                    throw new common_1.BadRequestException('Rwanda addresses must include province and district');
                }
            }
            const address = await this.prisma.address.create({
                data: createAddressDto
            });
            return address;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error('Failed to create address', error.stack);
            throw new Error('Failed to create address');
        }
    }
    async update(id, updateAddressDto, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingAddress) {
                throw new common_1.NotFoundException('Address not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== existingAddress.organizationId) {
                throw new common_1.ForbiddenException('You can only update addresses for your own organization');
            }
            if (updateAddressDto.addressType && updateAddressDto.addressType !== existingAddress.addressType) {
                const conflictingAddress = await this.prisma.address.findFirst({
                    where: {
                        organizationId: existingAddress.organizationId,
                        addressType: updateAddressDto.addressType,
                        deleted: false,
                        id: { not: id }
                    }
                });
                if (conflictingAddress) {
                    throw new common_1.ConflictException(`Organization already has a ${updateAddressDto.addressType} address`);
                }
            }
            const finalAddressType = updateAddressDto.addressType || existingAddress.addressType;
            if (finalAddressType === dto_1.AddressType.RWANDA) {
                const finalProvince = updateAddressDto.province || existingAddress.province;
                const finalDistrict = updateAddressDto.district || existingAddress.district;
                if (!finalProvince || !finalDistrict) {
                    throw new common_1.BadRequestException('Rwanda addresses must include province and district');
                }
            }
            const address = await this.prisma.address.update({
                where: { id },
                data: updateAddressDto
            });
            return address;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException ||
                error instanceof common_1.ForbiddenException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to update address ${id}`, error.stack);
            throw new Error('Failed to update address');
        }
    }
    async remove(id, currentUserId) {
        try {
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });
            if (!currentUser) {
                throw new common_1.NotFoundException('Current user not found');
            }
            const existingAddress = await this.prisma.address.findFirst({
                where: {
                    id,
                    deleted: false
                }
            });
            if (!existingAddress) {
                throw new common_1.NotFoundException('Address not found');
            }
            if (currentUser.role !== 'ADMIN' && currentUser.organizationId !== existingAddress.organizationId) {
                throw new common_1.ForbiddenException('You can only delete addresses for your own organization');
            }
            const totalAddressCount = await this.prisma.address.count({
                where: {
                    organizationId: existingAddress.organizationId,
                    deleted: false
                }
            });
            if (totalAddressCount <= 1) {
                throw new common_1.BadRequestException('Cannot delete the only address. Organizations must have at least one address (either Rwanda or headquarters).');
            }
            await this.prisma.address.update({
                where: { id },
                data: { deleted: true }
            });
            return { message: 'Address deleted successfully' };
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ForbiddenException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            this.logger.error(`Failed to delete address ${id}`, error.stack);
            throw new Error('Failed to delete address');
        }
    }
};
exports.AddressesService = AddressesService;
exports.AddressesService = AddressesService = AddressesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AddressesService);
//# sourceMappingURL=addresses.service.js.map