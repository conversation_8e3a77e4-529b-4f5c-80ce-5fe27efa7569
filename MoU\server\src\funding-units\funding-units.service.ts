import { Injectable, NotFoundException, ConflictException, ForbiddenException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingUnitDto, UpdateFundingUnitDto } from './dto';

@Injectable()
export class FundingUnitsService {
    private readonly logger = new Logger(FundingUnitsService.name);

    constructor(private prisma: PrismaService) {}

    async findAll() {
        try {
            const fundingUnits = await this.prisma.fundingUnit.findMany({
                where: { deleted: false },
                orderBy: { createAt: 'desc' }
            });

            return fundingUnits;
        } catch (error) {
            this.logger.error('Failed to fetch funding units', error.stack);
            throw new Error('Failed to fetch funding units');
        }
    }

    async findOne(id: number) {
        try {
            const fundingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });

            if (!fundingUnit) {
                throw new NotFoundException('Funding unit not found');
            }

            return fundingUnit;
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to fetch funding unit');
        }
    }

    async create(createFundingUnitDto: CreateFundingUnitDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can create funding units
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can create funding units');
            }

            // Check if funding unit with same name already exists
            const existingUnit = await this.prisma.fundingUnit.findFirst({
                where: { 
                    unitName: createFundingUnitDto.unitName,
                    deleted: false 
                }
            });

            if (existingUnit) {
                throw new ConflictException('Funding unit with this name already exists');
            }

            const fundingUnit = await this.prisma.fundingUnit.create({
                data: {
                    unitName: createFundingUnitDto.unitName
                }
            });

            return fundingUnit;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error('Failed to create funding unit', error.stack);
            throw new Error('Failed to create funding unit');
        }
    }

    async update(id: number, updateFundingUnitDto: UpdateFundingUnitDto, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can update funding units
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can update funding units');
            }

            // Check if funding unit exists
            const existingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });

            if (!existingUnit) {
                throw new NotFoundException('Funding unit not found');
            }

            // Check if new name conflicts with existing units
            if (updateFundingUnitDto.unitName && updateFundingUnitDto.unitName !== existingUnit.unitName) {
                const nameConflict = await this.prisma.fundingUnit.findFirst({
                    where: { 
                        unitName: updateFundingUnitDto.unitName,
                        deleted: false,
                        id: { not: id }
                    }
                });

                if (nameConflict) {
                    throw new ConflictException('Funding unit with this name already exists');
                }
            }

            const fundingUnit = await this.prisma.fundingUnit.update({
                where: { id },
                data: updateFundingUnitDto
            });

            return fundingUnit;
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to update funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to update funding unit');
        }
    }

    async remove(id: number, currentUserId: string) {
        try {
            // Get current user to check permissions
            const currentUser = await this.prisma.user.findUnique({
                where: { id: currentUserId }
            });

            if (!currentUser) {
                throw new NotFoundException('Current user not found');
            }

            // Only ADMIN can delete funding units
            if (currentUser.role !== 'ADMIN') {
                throw new ForbiddenException('Only administrators can delete funding units');
            }

            // Check if funding unit exists
            const existingUnit = await this.prisma.fundingUnit.findUnique({
                where: { id, deleted: false }
            });

            if (!existingUnit) {
                throw new NotFoundException('Funding unit not found');
            }

            // Check if funding unit is being used by any projects
            const projectsUsingUnit = await this.prisma.project.findFirst({
                where: { 
                    fundingUnitId: id,
                    deleted: false 
                }
            });

            if (projectsUsingUnit) {
                throw new ConflictException('Cannot delete funding unit that is being used by projects');
            }

            // Soft delete the funding unit
            await this.prisma.fundingUnit.update({
                where: { id },
                data: { deleted: true }
            });

            return { message: 'Funding unit deleted successfully' };
        } catch (error) {
            if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
                throw error;
            }
            this.logger.error(`Failed to delete funding unit with ID ${id}`, error.stack);
            throw new Error('Failed to delete funding unit');
        }
    }
}
