import { ApiProperty } from '@nestjs/swagger';

export class MouResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the MoU',
    example: 'uuid-mou-id'
  })
  id: string;

  @ApiProperty({
    description: 'MoU identifier',
    example: 'MOU-2024-001'
  })
  mouId: string;

  @ApiProperty({
    description: 'Party ID associated with this MoU',
    example: 'uuid-party-id'
  })
  partyId: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00.000Z'
  })
  createAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-01T00:00:00.000Z'
  })
  updatedAt: string;

  @ApiProperty({
    description: 'Soft delete flag',
    example: false
  })
  deleted: boolean;

  @ApiProperty({
    description: 'Associated party information',
    required: false
  })
  party?: {
    id: string;
    name: string;
    signatory: string;
    position: string;
  };
}
