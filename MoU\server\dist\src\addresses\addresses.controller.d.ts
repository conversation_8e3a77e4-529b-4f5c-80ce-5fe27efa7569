import { AddressesService } from './addresses.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
export declare class AddressesController {
    private readonly addressesService;
    constructor(addressesService: AddressesService);
    findByOrganization(organizationId: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationId: string;
        createdAt: Date;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationId: string;
        createdAt: Date;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
    }>;
    create(createAddressDto: CreateAddressDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationId: string;
        createdAt: Date;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
    }>;
    update(id: string, updateAddressDto: UpdateAddressDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        organizationId: string;
        createdAt: Date;
        addressType: import("@prisma/client").$Enums.AddressType;
        country: string;
        province: string | null;
        district: string | null;
        sector: string | null;
        cell: string | null;
        village: string | null;
        street: string;
        avenue: string | null;
        poBox: string;
        postalCode: string | null;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
