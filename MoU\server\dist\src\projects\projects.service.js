"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let ProjectsService = class ProjectsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createProjectDto, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const existingProject = await this.prisma.project.findUnique({
            where: { projectId: createProjectDto.projectId }
        });
        if (existingProject) {
            throw new common_1.ConflictException('Project with this ID already exists');
        }
        const mouApplication = await this.prisma.mouApplication.findUnique({
            where: { id: createProjectDto.mouApplicationId }
        });
        if (!mouApplication) {
            throw new common_1.NotFoundException('MoU application not found');
        }
        if (user.role !== 'ADMIN' && mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const [budgetType, fundingUnit, fundingSource, organization, document] = await Promise.all([
            this.prisma.budgetType.findUnique({ where: { id: createProjectDto.budgetTypeId } }),
            this.prisma.fundingUnit.findUnique({ where: { id: createProjectDto.fundingUnitId } }),
            this.prisma.fundingSource.findUnique({ where: { id: createProjectDto.fundingSourceId } }),
            this.prisma.organization.findUnique({ where: { id: createProjectDto.organizationId } }),
            this.prisma.document.findUnique({ where: { id: createProjectDto.projectDocumentId } })
        ]);
        if (!budgetType)
            throw new common_1.NotFoundException('Budget type not found');
        if (!fundingUnit)
            throw new common_1.NotFoundException('Funding unit not found');
        if (!fundingSource)
            throw new common_1.NotFoundException('Funding source not found');
        if (!organization)
            throw new common_1.NotFoundException('Organization not found');
        if (!document)
            throw new common_1.NotFoundException('Document not found');
        return this.prisma.project.create({
            data: {
                ...createProjectDto,
                startDate: createProjectDto.startDate ? new Date(createProjectDto.startDate) : null,
                endDate: createProjectDto.endDate ? new Date(createProjectDto.endDate) : null,
            },
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: {
                    select: {
                        id: true,
                        organizationName: true,
                    }
                },
                projectActivities: true,
                activities: true,
            }
        });
    }
    async findAll(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const whereClause = user.role === 'ADMIN'
            ? { deleted: false }
            : {
                deleted: false,
                organizationId: user.organizationId
            };
        return this.prisma.project.findMany({
            where: whereClause,
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: {
                    select: {
                        id: true,
                        organizationName: true,
                    }
                },
                projectActivities: true,
                activities: true,
            },
            orderBy: { createAt: 'desc' }
        });
    }
    async findOne(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const project = await this.prisma.project.findUnique({
            where: { id, deleted: false },
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: {
                    select: {
                        id: true,
                        organizationName: true,
                    }
                },
                projectActivities: {
                    where: { deleted: false },
                    orderBy: { createdAt: 'asc' }
                },
                activities: {
                    where: { deleted: false },
                    orderBy: { createAt: 'asc' }
                },
                mouApplication: {
                    select: {
                        id: true,
                        mouApplicationId: true,
                        userId: true,
                        status: true,
                    }
                }
            }
        });
        if (!project) {
            throw new common_1.NotFoundException('Project not found');
        }
        if (user.role !== 'ADMIN' &&
            project.organizationId !== user.organizationId &&
            project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        return project;
    }
    async update(id, updateProjectDto, userId) {
        const project = await this.findOne(id, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (updateProjectDto.projectId && updateProjectDto.projectId !== project.projectId) {
            const existingProject = await this.prisma.project.findUnique({
                where: { projectId: updateProjectDto.projectId }
            });
            if (existingProject) {
                throw new common_1.ConflictException('Project with this ID already exists');
            }
        }
        const updateData = { ...updateProjectDto };
        if (updateProjectDto.startDate)
            updateData.startDate = new Date(updateProjectDto.startDate);
        if (updateProjectDto.endDate)
            updateData.endDate = new Date(updateProjectDto.endDate);
        return this.prisma.project.update({
            where: { id },
            data: updateData,
            include: {
                budgetType: true,
                fundingUnit: true,
                fundingSource: true,
                organization: {
                    select: {
                        id: true,
                        organizationName: true,
                    }
                },
                projectActivities: true,
                activities: true,
            }
        });
    }
    async createActivity(projectId, createProjectActivityDto, userId) {
        const project = await this.findOne(projectId, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        const existingActivity = await this.prisma.projectActivity.findUnique({
            where: { activityId: createProjectActivityDto.activityId }
        });
        if (existingActivity) {
            throw new common_1.ConflictException('Activity with this ID already exists');
        }
        return this.prisma.projectActivity.create({
            data: {
                ...createProjectActivityDto,
                projectId,
            }
        });
    }
    async findActivities(projectId, userId) {
        const project = await this.findOne(projectId, userId);
        return this.prisma.projectActivity.findMany({
            where: {
                projectId,
                deleted: false
            },
            orderBy: { createdAt: 'asc' }
        });
    }
    async findActivity(projectId, activityId, userId) {
        const project = await this.findOne(projectId, userId);
        const activity = await this.prisma.projectActivity.findFirst({
            where: {
                id: activityId,
                projectId,
                deleted: false
            }
        });
        if (!activity) {
            throw new common_1.NotFoundException('Activity not found');
        }
        return activity;
    }
    async updateActivity(projectId, activityId, updateProjectActivityDto, userId) {
        const project = await this.findOne(projectId, userId);
        const activity = await this.findActivity(projectId, activityId, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (updateProjectActivityDto.activityId && updateProjectActivityDto.activityId !== activity.activityId) {
            const existingActivity = await this.prisma.projectActivity.findUnique({
                where: { activityId: updateProjectActivityDto.activityId }
            });
            if (existingActivity) {
                throw new common_1.ConflictException('Activity with this ID already exists');
            }
        }
        return this.prisma.projectActivity.update({
            where: { id: activityId },
            data: updateProjectActivityDto
        });
    }
    async deleteActivity(projectId, activityId, userId) {
        const project = await this.findOne(projectId, userId);
        const activity = await this.findActivity(projectId, activityId, userId);
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        await this.prisma.projectActivity.update({
            where: { id: activityId },
            data: { deleted: true }
        });
        return { message: 'Activity deleted successfully' };
    }
    async remove(id, userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId }
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const project = await this.findOne(id, userId);
        if (user.role !== 'ADMIN' && project.mouApplication.userId !== userId) {
            throw new common_1.ForbiddenException('Access denied');
        }
        if (project.mouApplication && project.mouApplication.status !== 'DRAFT') {
            throw new common_1.BadRequestException('Cannot delete project from submitted application');
        }
        await this.prisma.project.update({
            where: { id },
            data: { deleted: true }
        });
        return { message: 'Project deleted successfully' };
    }
};
exports.ProjectsService = ProjectsService;
exports.ProjectsService = ProjectsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ProjectsService);
//# sourceMappingURL=projects.service.js.map