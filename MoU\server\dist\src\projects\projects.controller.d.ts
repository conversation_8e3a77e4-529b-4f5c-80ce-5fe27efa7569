import { ProjectsService } from './projects.service';
import { CreateProjectDto, UpdateProjectDto, CreateProjectActivityDto, UpdateProjectActivityDto } from './dto';
export declare class ProjectsController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto, req: any): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    findAll(req: any): Promise<({
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    })[]>;
    findOne(id: string, req: any): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        mouApplication: {
            id: string;
            userId: string;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApplicationStatus;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    update(id: string, updateProjectDto: UpdateProjectDto, req: any): Promise<{
        organization: {
            id: string;
            organizationName: string;
        };
        budgetType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        fundingUnit: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            unitName: string;
        };
        fundingSource: {
            id: number;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            sourceName: string;
        };
        activities: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            description: string | null;
            projectId: string;
            startDate: Date;
            endDate: Date;
            activityId: string;
            implementer: string;
            implementerUnit: string;
            fiscalYear: number;
            domainOfInterventionId: number;
            inputId: string;
        }[];
        projectActivities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            currency: string | null;
            description: string | null;
            projectId: string;
            activityId: string;
            activityName: string;
            timeline: string | null;
            budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        name: string;
        organizationId: string;
        currency: string | null;
        description: string | null;
        projectId: string;
        duration: number;
        startDate: Date | null;
        endDate: Date | null;
        totalBudget: import("@prisma/client/runtime/library").Decimal | null;
        budgetTypeId: number;
        fundingUnitId: number;
        fundingSourceId: number;
        projectDocumentId: string;
        mouApplicationId: string;
    }>;
    createActivity(id: string, createProjectActivityDto: CreateProjectActivityDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    findActivities(id: string, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }[]>;
    findActivity(id: string, activityId: string, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    updateActivity(id: string, activityId: string, updateProjectActivityDto: UpdateProjectActivityDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        currency: string | null;
        description: string | null;
        projectId: string;
        activityId: string;
        activityName: string;
        timeline: string | null;
        budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    deleteActivity(id: string, activityId: string, req: any): Promise<{
        message: string;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
