import { Injectable, ConflictException, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';

@Injectable()
export class DomainInterventionsService {
  constructor(private prisma: PrismaService) {}

  async create(createDomainInterventionDto: CreateDomainInterventionDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can create domain interventions');
    }

    // If parentId is provided, check if parent exists
    if (createDomainInterventionDto.parentId) {
      const parentDomain = await this.prisma.domainIntervention.findUnique({
        where: { id: createDomainInterventionDto.parentId, deleted: false },
      });

      if (!parentDomain) {
        throw new BadRequestException('Parent domain not found');
      }
    }

    return this.prisma.domainIntervention.create({
      data: createDomainInterventionDto,
      include: {
        parent: true,
        children: true,
      },
    });
  }

  async findAll() {
    return this.prisma.domainIntervention.findMany({
      where: { deleted: false },
      include: {
        parent: true,
        children: {
          where: { deleted: false },
          include: {
            children: {
              where: { deleted: false },
              include: {
                children: true, // Support unlimited nesting
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findTree() {
    // Get all domains first
    const allDomains = await this.prisma.domainIntervention.findMany({
      where: { deleted: false },
      orderBy: { createdAt: 'desc' },
    });

    // Build the tree structure recursively
    const buildTree = (parentId: number | null): any[] => {
      return allDomains
        .filter(domain => domain.parentId === parentId)
        .map(domain => ({
          ...domain,
          children: buildTree(domain.id),
        }));
    };

    // Return only root domains (parentId: null) with their full hierarchy
    return buildTree(null);
  }

  async findOne(id: number) {
    const domain = await this.prisma.domainIntervention.findUnique({
      where: { id, deleted: false },
      include: {
        parent: true,
      },
    });

    if (!domain) {
      throw new NotFoundException('Domain intervention not found');
    }

    // Get all children recursively
    const allDomains = await this.prisma.domainIntervention.findMany({
      where: { deleted: false },
    });

    const buildChildren = (parentId: number): any[] => {
      return allDomains
        .filter(d => d.parentId === parentId)
        .map(child => ({
          ...child,
          children: buildChildren(child.id),
        }));
    };

    return {
      ...domain,
      children: buildChildren(domain.id),
    };
  }

  async update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can update domain interventions');
    }

    // Check if domain exists
    const existingDomain = await this.prisma.domainIntervention.findUnique({
      where: { id, deleted: false },
    });

    if (!existingDomain) {
      throw new NotFoundException('Domain intervention not found');
    }

    // If parentId is being updated, validate it
    if (updateDomainInterventionDto.parentId !== undefined) {
      if (updateDomainInterventionDto.parentId === id) {
        throw new BadRequestException('Domain cannot be its own parent');
      }

      if (updateDomainInterventionDto.parentId) {
        const parentDomain = await this.prisma.domainIntervention.findUnique({
          where: { id: updateDomainInterventionDto.parentId, deleted: false },
        });

        if (!parentDomain) {
          throw new BadRequestException('Parent domain not found');
        }

        // Check for circular reference
        const isCircular = await this.checkCircularReference(id, updateDomainInterventionDto.parentId);
        if (isCircular) {
          throw new BadRequestException('Circular reference detected');
        }
      }
    }

    return this.prisma.domainIntervention.update({
      where: { id },
      data: updateDomainInterventionDto,
      include: {
        parent: true,
        children: true,
      },
    });
  }

  async remove(id: number, userId: string) {
    // Check if user is admin
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only admins can delete domain interventions');
    }

    // Check if domain exists
    const existingDomain = await this.prisma.domainIntervention.findUnique({
      where: { id, deleted: false },
      include: {
        children: {
          where: { deleted: false },
        },
      },
    });

    if (!existingDomain) {
      throw new NotFoundException('Domain intervention not found');
    }

    // Check if domain has children
    if (existingDomain.children.length > 0) {
      throw new BadRequestException('Cannot delete domain with sub-domains. Delete sub-domains first.');
    }

    // Soft delete
    return this.prisma.domainIntervention.update({
      where: { id },
      data: { deleted: true },
    });
  }

  private async checkCircularReference(domainId: number, parentId: number): Promise<boolean> {
    if (domainId === parentId) {
      return true;
    }

    const parent = await this.prisma.domainIntervention.findUnique({
      where: { id: parentId },
    });

    if (!parent || !parent.parentId) {
      return false;
    }

    return this.checkCircularReference(domainId, parent.parentId);
  }
}
