"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const platform_express_1 = require("@nestjs/platform-express");
const mou_applications_service_1 = require("./mou-applications.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
let MouApplicationsController = class MouApplicationsController {
    constructor(mouApplicationsService) {
        this.mouApplicationsService = mouApplicationsService;
    }
    async create(createMouApplicationDto, req) {
        return this.mouApplicationsService.create(createMouApplicationDto, req.user.sub);
    }
    async createDraft(mouId, req) {
        return this.mouApplicationsService.createDraft(mouId, req.user.sub);
    }
    async findAll(req) {
        return this.mouApplicationsService.findAll(req.user.sub);
    }
    async findMyApplications(req) {
        return this.mouApplicationsService.findMyApplications(req.user.sub);
    }
    async findOne(id, req) {
        return this.mouApplicationsService.findOne(id, req.user.sub);
    }
    async update(id, updateMouApplicationDto, req) {
        return this.mouApplicationsService.update(id, updateMouApplicationDto, req.user.sub);
    }
    async updateStep(id, stepNumber, updateStepDto, req) {
        return this.mouApplicationsService.updateStep(id, stepNumber, updateStepDto, req.user.sub);
    }
    async autoSave(id, autoSaveDto, req) {
        return this.mouApplicationsService.autoSave(id, autoSaveDto, req.user.sub);
    }
    async createResponsibility(id, createResponsibilityDto, req) {
        return this.mouApplicationsService.createResponsibility(id, createResponsibilityDto, req.user.sub);
    }
    async updateResponsibility(id, responsibilityId, updateData, req) {
        return this.mouApplicationsService.updateResponsibility(id, responsibilityId, updateData, req.user.sub);
    }
    async deleteResponsibility(id, responsibilityId, req) {
        return this.mouApplicationsService.deleteResponsibility(id, responsibilityId, req.user.sub);
    }
    async uploadDocument(id, file, uploadDocumentDto, req) {
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        return this.mouApplicationsService.uploadDocument(id, file, uploadDocumentDto, req.user.sub);
    }
    async deleteDocument(id, documentId, req) {
        return this.mouApplicationsService.deleteDocument(id, documentId, req.user.sub);
    }
    async remove(id, req) {
        return this.mouApplicationsService.remove(id, req.user.sub);
    }
};
exports.MouApplicationsController = MouApplicationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new MoU application' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Application created successfully', type: dto_1.MouApplicationResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Application with ID already exists' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU not found' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateMouApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('draft'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a draft MoU application with minimal data' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Draft application created successfully', type: dto_1.MouApplicationResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU not found' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                mouId: { type: 'string', description: 'MoU ID' }
            },
            required: ['mouId']
        }
    }),
    __param(0, (0, common_1.Body)('mouId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "createDraft", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all MoU applications (Admin sees all, others see their own)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of applications', type: [dto_1.MouApplicationResponseDto] }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('my-applications'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current user\'s MoU applications' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns user\'s applications', type: [dto_1.MouApplicationResponseDto] }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findMyApplications", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get MoU application by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns application details', type: dto_1.MouApplicationResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a MoU application' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application updated successfully', type: dto_1.MouApplicationResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Application with ID already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateMouApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/step/:stepNumber'),
    (0, swagger_1.ApiOperation)({ summary: 'Update specific step data with validation' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiParam)({ name: 'stepNumber', description: 'Step number (1-5)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Step updated successfully', type: dto_1.MouApplicationResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid step number' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('stepNumber', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, dto_1.UpdateStepDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "updateStep", null);
__decorate([
    (0, common_1.Post)(':id/auto-save'),
    (0, swagger_1.ApiOperation)({ summary: 'Auto-save draft functionality for form progress' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Auto-save successful' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.AutoSaveDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "autoSave", null);
__decorate([
    (0, common_1.Post)(':id/responsibilities'),
    (0, swagger_1.ApiOperation)({ summary: 'Create and manage MoU responsibilities' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Responsibility created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.CreateResponsibilityDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "createResponsibility", null);
__decorate([
    (0, common_1.Patch)(':id/responsibilities/:responsibilityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a responsibility' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiParam)({ name: 'responsibilityId', description: 'Responsibility ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Responsibility updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Responsibility not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('responsibilityId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "updateResponsibility", null);
__decorate([
    (0, common_1.Delete)(':id/responsibilities/:responsibilityId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a responsibility' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiParam)({ name: 'responsibilityId', description: 'Responsibility ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Responsibility deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Responsibility not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('responsibilityId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "deleteResponsibility", null);
__decorate([
    (0, common_1.Post)(':id/documents'),
    (0, swagger_1.ApiOperation)({ summary: 'Handle document file uploads with proper validation' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: 'Document file to upload'
                },
                documentType: {
                    type: 'string',
                    description: 'Type/category of the document'
                },
                isRequired: {
                    type: 'boolean',
                    description: 'Whether this document is required'
                }
            },
            required: ['file', 'documentType']
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Document uploaded successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid file type or no file provided' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, dto_1.UploadDocumentDto, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "uploadDocument", null);
__decorate([
    (0, common_1.Delete)(':id/documents/:documentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a document' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiParam)({ name: 'documentId', description: 'Document ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Document deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Document not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('documentId')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "deleteDocument", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a MoU application (only drafts)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Application ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Only draft applications can be deleted' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Application not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouApplicationsController.prototype, "remove", null);
exports.MouApplicationsController = MouApplicationsController = __decorate([
    (0, swagger_1.ApiTags)('mou-applications'),
    (0, common_1.Controller)('mou-applications'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [mou_applications_service_1.MouApplicationsService])
], MouApplicationsController);
//# sourceMappingURL=mou-applications.controller.js.map