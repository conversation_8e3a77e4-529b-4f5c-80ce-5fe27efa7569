"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouApplicationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const client_1 = require("@prisma/client");
class MouApplicationResponseDto {
}
exports.MouApplicationResponseDto = MouApplicationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the MoU application',
        example: 'uuid-application-id'
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU application identifier',
        example: 'APP-2024-001'
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "mouApplicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU ID this application is for',
        example: 'uuid-mou-id'
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "mouId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application responsibility description',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application status',
        enum: client_1.ApplicationStatus
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current step in the application process (1-5)',
        minimum: 1,
        maximum: 5
    }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "currentStep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU duration in years',
        required: false
    }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "mouDurationYears", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the signatory',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "signatoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Position of the signatory',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "signatoryPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party name for this application',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "partyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Completion percentage (0-100)',
        minimum: 0,
        maximum: 100
    }),
    __metadata("design:type", Number)
], MouApplicationResponseDto.prototype, "completionPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last auto-save timestamp',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "lastAutoSave", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who created this application',
        required: false
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation timestamp'
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update timestamp'
    }),
    __metadata("design:type", String)
], MouApplicationResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Soft delete flag'
    }),
    __metadata("design:type", Boolean)
], MouApplicationResponseDto.prototype, "deleted", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated user information',
        required: false
    }),
    __metadata("design:type", Object)
], MouApplicationResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated MoU information',
        required: false
    }),
    __metadata("design:type", Object)
], MouApplicationResponseDto.prototype, "mou", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Associated projects',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], MouApplicationResponseDto.prototype, "projects", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application responsibilities',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], MouApplicationResponseDto.prototype, "responsibilities", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application documents',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], MouApplicationResponseDto.prototype, "documents", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Approval steps',
        required: false,
        isArray: true
    }),
    __metadata("design:type", Array)
], MouApplicationResponseDto.prototype, "approvalSteps", void 0);
//# sourceMappingURL=mou-application-response.dto.js.map