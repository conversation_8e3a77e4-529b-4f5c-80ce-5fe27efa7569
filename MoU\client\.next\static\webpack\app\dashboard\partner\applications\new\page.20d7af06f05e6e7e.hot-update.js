"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/step1-mou-information.tsx":
/*!***********************************************************!*\
  !*** ./components/wizard-steps/step1-mou-information.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Step1MouInformation: () => (/* binding */ Step1MouInformation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Info!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/mou-application.service */ \"(app-pages-browser)/./lib/services/mou-application.service.ts\");\n/* __next_internal_client_entry_do_not_use__ Step1MouInformation auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Step1MouInformation(param) {\n    let { data, onChange, application } = param;\n    _s();\n    const [availableMous, setAvailableMous] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedMou, setSelectedMou] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Step1MouInformation.useEffect\": ()=>{\n            loadAvailableMous();\n        }\n    }[\"Step1MouInformation.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Step1MouInformation.useEffect\": ()=>{\n            if (data.mouId && availableMous.length > 0) {\n                const mou = availableMous.find({\n                    \"Step1MouInformation.useEffect.mou\": (m)=>m.id === data.mouId\n                }[\"Step1MouInformation.useEffect.mou\"]);\n                setSelectedMou(mou || null);\n            }\n        }\n    }[\"Step1MouInformation.useEffect\"], [\n        data.mouId,\n        availableMous\n    ]);\n    const loadAvailableMous = async ()=>{\n        try {\n            setLoading(true);\n            const mous = await _lib_services_mou_application_service__WEBPACK_IMPORTED_MODULE_7__.mouApplicationService.getAvailableMous();\n            setAvailableMous(mous);\n        } catch (err) {\n            setError(\"Failed to load available MoUs\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleMouChange = (mouId)=>{\n        const mou = availableMous.find((m)=>m.id === mouId);\n        setSelectedMou(mou || null);\n        onChange({\n            ...data,\n            mouId\n        });\n    };\n    const handleDurationChange = (value)=>{\n        const years = parseInt(value);\n        if (years >= 1 && years <= 10) {\n            onChange({\n                ...data,\n                mouDurationYears: years\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-cyan-900 mb-2\",\n                        children: \"MoU Information\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Select the Memorandum of Understanding you want to apply for and set the duration.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2 text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyan-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Organization Information\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Your organization details (read-only)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            htmlFor: \"organizationName\",\n                                            children: \"Organization Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"organizationName\",\n                                            value: data.organizationName,\n                                            readOnly: true,\n                                            className: \"bg-gray-50\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"This information is automatically populated from your account\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center gap-2 text-base\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 text-cyan-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"MoU Duration\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                        children: \"Set the duration for this MoU agreement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                            htmlFor: \"mouDurationYears\",\n                                            children: \"Duration (Years) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n                                            value: data.mouDurationYears.toString(),\n                                            onValueChange: handleDurationChange,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                                                        placeholder: \"Select duration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                                                    children: Array.from({\n                                                        length: 10\n                                                    }, (_, i)=>i + 1).map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                                                            value: year.toString(),\n                                                            children: [\n                                                                year,\n                                                                \" \",\n                                                                year === 1 ? 'Year' : 'Years'\n                                                            ]\n                                                        }, year, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Duration must be between 1 and 10 years\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-cyan-50 border-cyan-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Info_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-cyan-600 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-medium text-cyan-900\",\n                                        children: \"Step 1 Requirements\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-cyan-800 mt-2 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2 \".concat(data.mouId ? 'text-green-700' : ''),\n                                                children: [\n                                                    data.mouId ? '✓' : '○',\n                                                    \" Select a MoU\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2 \".concat(data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? 'text-green-700' : ''),\n                                                children: [\n                                                    data.mouDurationYears >= 1 && data.mouDurationYears <= 10 ? '✓' : '○',\n                                                    \" Set MoU duration (1-10 years)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\CODE\\\\WORK\\\\MoH\\\\MoU\\\\client\\\\components\\\\wizard-steps\\\\step1-mou-information.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(Step1MouInformation, \"kxVYKBDRbuhbtnHa4LImdULfyiM=\");\n_c = Step1MouInformation;\nvar _c;\n$RefreshReg$(_c, \"Step1MouInformation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/step1-mou-information.tsx\n"));

/***/ })

});