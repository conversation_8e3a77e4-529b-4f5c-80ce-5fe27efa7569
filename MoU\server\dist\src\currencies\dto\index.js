"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CurrencyResponseDto = exports.UpdateCurrencyDto = exports.CreateCurrencyDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCurrencyDto {
}
exports.CreateCurrencyDto = CreateCurrencyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency code (e.g., USD, EUR, RWF)', example: 'USD' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(3, 3, { message: 'Currency code must be exactly 3 characters' }),
    __metadata("design:type", String)
], CreateCurrencyDto.prototype, "currencyCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency name (e.g., US Dollar, Euro, Rwandan Franc)', example: 'US Dollar' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCurrencyDto.prototype, "currencyName", void 0);
class UpdateCurrencyDto {
}
exports.UpdateCurrencyDto = UpdateCurrencyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Currency code (e.g., USD, EUR, RWF)', example: 'USD' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(3, 3, { message: 'Currency code must be exactly 3 characters' }),
    __metadata("design:type", String)
], UpdateCurrencyDto.prototype, "currencyCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false, description: 'Currency name (e.g., US Dollar, Euro, Rwandan Franc)', example: 'US Dollar' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateCurrencyDto.prototype, "currencyName", void 0);
class CurrencyResponseDto {
}
exports.CurrencyResponseDto = CurrencyResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency ID', example: 1 }),
    __metadata("design:type", Number)
], CurrencyResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency code', example: 'USD' }),
    __metadata("design:type", String)
], CurrencyResponseDto.prototype, "currencyCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency name', example: 'US Dollar' }),
    __metadata("design:type", String)
], CurrencyResponseDto.prototype, "currencyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Creation date' }),
    __metadata("design:type", Date)
], CurrencyResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Last update date' }),
    __metadata("design:type", Date)
], CurrencyResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Soft delete flag' }),
    __metadata("design:type", Boolean)
], CurrencyResponseDto.prototype, "deleted", void 0);
//# sourceMappingURL=index.js.map