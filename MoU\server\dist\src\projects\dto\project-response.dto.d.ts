export declare class ProjectResponseDto {
    id: string;
    projectId: string;
    name: string;
    description?: string;
    duration: number;
    startDate?: string;
    endDate?: string;
    totalBudget?: number;
    currency?: string;
    budgetTypeId: number;
    fundingUnitId: number;
    fundingSourceId: number;
    organizationId: string;
    projectDocumentId: string;
    mouApplicationId: string;
    createAt: string;
    updatedAt: string;
    deleted: boolean;
    budgetType?: {
        id: number;
        typeName: string;
    };
    fundingUnit?: {
        id: number;
        unitName: string;
    };
    fundingSource?: {
        id: number;
        sourceName: string;
    };
    organization?: {
        id: string;
        organizationName: string;
    };
    projectActivities?: any[];
    activities?: any[];
}
