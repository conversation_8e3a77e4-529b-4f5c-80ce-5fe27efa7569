import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, IsArray, ValidateNested, IsNumber, IsDateString, IsEnum, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { ApplicationStatus } from '@prisma/client';

export class UpdateStepDto {
  @ApiProperty({
    description: 'Application responsibility description',
    required: false
  })
  @IsOptional()
  @IsString()
  responsibility?: string;

  @ApiProperty({
    description: 'MoU duration in years',
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  mouDurationYears?: number;

  @ApiProperty({
    description: 'Name of the signatory',
    required: false
  })
  @IsOptional()
  @IsString()
  signatoryName?: string;

  @ApiProperty({
    description: 'Position of the signatory',
    required: false
  })
  @IsOptional()
  @IsString()
  signatoryPosition?: string;

  @ApiProperty({
    description: 'Party name for this application',
    required: false
  })
  @IsOptional()
  @IsString()
  partyName?: string;

  @ApiProperty({
    description: 'Completion percentage (0-100)',
    minimum: 0,
    maximum: 100,
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  completionPercentage?: number;

  @ApiProperty({
    description: 'Application status',
    enum: ApplicationStatus,
    required: false
  })
  @IsOptional()
  @IsEnum(ApplicationStatus)
  status?: ApplicationStatus;

  @ApiProperty({
    description: 'Project data for step 2',
    required: false
  })
  @IsOptional()
  projectData?: any;

  @ApiProperty({
    description: 'Activity data for step 3',
    required: false
  })
  @IsOptional()
  activityData?: any;

  @ApiProperty({
    description: 'Document data for step 4',
    required: false
  })
  @IsOptional()
  documentData?: any;

  @ApiProperty({
    description: 'Review data for step 5',
    required: false
  })
  @IsOptional()
  reviewData?: any;
}
