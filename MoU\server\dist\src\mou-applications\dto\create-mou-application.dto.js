"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateMouApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const client_1 = require("@prisma/client");
class CreateMouApplicationDto {
}
exports.CreateMouApplicationDto = CreateMouApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique MoU application identifier',
        example: 'APP-2024-001'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "mouApplicationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU ID this application is for',
        example: 'uuid-mou-id'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "mouId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application responsibility description',
        required: false,
        example: 'Healthcare service delivery in rural areas'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "responsibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application status',
        enum: client_1.ApplicationStatus,
        default: client_1.ApplicationStatus.DRAFT,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(client_1.ApplicationStatus),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current step in the application process (1-5)',
        minimum: 1,
        maximum: 5,
        default: 1,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], CreateMouApplicationDto.prototype, "currentStep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'MoU duration in years',
        required: false,
        example: 3
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateMouApplicationDto.prototype, "mouDurationYears", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the signatory',
        required: false,
        example: 'Dr. John Doe'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "signatoryName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Position of the signatory',
        required: false,
        example: 'Chief Executive Officer'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "signatoryPosition", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Party name for this application',
        required: false,
        example: 'Healthcare Partners International'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateMouApplicationDto.prototype, "partyName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Completion percentage (0-100)',
        minimum: 0,
        maximum: 100,
        default: 0,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateMouApplicationDto.prototype, "completionPercentage", void 0);
//# sourceMappingURL=create-mou-application.dto.js.map