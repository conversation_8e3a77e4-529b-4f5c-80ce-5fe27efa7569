"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, MoreHorizontal, Search, Edit, Trash2 } from "lucide-react"
import { masterDataService, type FundingUnit } from "@/lib/services/master-data.service"

export default function FundingUnitsPage() {
  const [fundingUnits, setFundingUnits] = useState<FundingUnit[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingUnit, setEditingUnit] = useState<FundingUnit | null>(null)
  const [unitName, setUnitName] = useState("")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    loadFundingUnits()
  }, [])

  const loadFundingUnits = async () => {
    try {
      setLoading(true)
      const data = await masterDataService.getFundingUnits()
      setFundingUnits(data)
    } catch (error) {
      console.error("Failed to load funding units:", error)
      setError("Failed to load funding units")
    } finally {
      setLoading(false)
    }
  }

  const filteredUnits = fundingUnits.filter((unit) =>
    unit.unitName.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")
    setSuccess("")

    try {
      if (editingUnit) {
        // Update existing unit
        await masterDataService.updateFundingUnit(editingUnit.id, { unitName })
        setSuccess("Funding unit updated successfully")
      } else {
        // Create new unit
        await masterDataService.createFundingUnit({ unitName })
        setSuccess("Funding unit created successfully")
      }

      // Reload the data to get the latest from server
      await loadFundingUnits()
      setDialogOpen(false)
      setUnitName("")
      setEditingUnit(null)
    } catch (err: any) {
      console.error("Failed to save funding unit:", err)
      setError(err.response?.data?.message || "Failed to save funding unit")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (unit: FundingUnit) => {
    setEditingUnit(unit)
    setUnitName(unit.unitName)
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (confirm("Are you sure you want to delete this funding unit?")) {
      try {
        await masterDataService.deleteFundingUnit(id)
        setSuccess("Funding unit deleted successfully")
        // Reload the data to get the latest from server
        await loadFundingUnits()
      } catch (err: any) {
        console.error("Failed to delete funding unit:", err)
        setError(err.response?.data?.message || "Failed to delete funding unit")
      }
    }
  }

  const resetForm = () => {
    setUnitName("")
    setEditingUnit(null)
    setError("")
    setSuccess("")
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Funding Units</h2>
        <Dialog
          open={dialogOpen}
          onOpenChange={(open) => {
            setDialogOpen(open)
            if (!open) resetForm()
          }}
        >
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Funding Unit
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>{editingUnit ? "Edit Funding Unit" : "Add Funding Unit"}</DialogTitle>
                <DialogDescription>
                  {editingUnit ? "Update the funding unit details." : "Create a new funding unit."}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="unitName">Unit Name</Label>
                  <Input
                    id="unitName"
                    value={unitName}
                    onChange={(e) => setUnitName(e.target.value)}
                    placeholder="Enter funding unit name"
                    required
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingUnit ? "Updating..." : "Creating..."}
                    </>
                  ) : editingUnit ? (
                    "Update"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search funding units..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Unit Name</TableHead>
              <TableHead>Created At</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin mx-auto" />
                  <p className="mt-2 text-muted-foreground">Loading funding units...</p>
                </TableCell>
              </TableRow>
            ) : filteredUnits.length === 0 ? (
              <TableRow>
                <TableCell colSpan={4} className="text-center py-8">
                  <p className="text-muted-foreground">
                    {searchTerm ? "No funding units found matching your search." : "No funding units found."}
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              filteredUnits.map((unit) => (
                <TableRow key={unit.id}>
                  <TableCell className="font-medium">{unit.unitName}</TableCell>
                  <TableCell>{new Date(unit.createAt).toLocaleDateString()}</TableCell>
                  <TableCell>{new Date(unit.updatedAt).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleEdit(unit)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleDelete(unit.id)} className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
