"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Plus, Search, Network, ChevronDown, ChevronRight } from "lucide-react"
import { TreeView, type TreeNode } from "@/components/ui/tree-view"
import { masterDataService, type DomainIntervention, type DomainInterventionHierarchy } from "@/lib/services/master-data.service"

export default function DomainInterventionsPage() {
  const [domains, setDomains] = useState<DomainIntervention[]>([])
  const [treeData, setTreeData] = useState<DomainInterventionHierarchy[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [loading, setLoading] = useState(true)
  const [formLoading, setFormLoading] = useState(false)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingDomain, setEditingDomain] = useState<DomainIntervention | null>(null)
  const [parentDomain, setParentDomain] = useState<DomainIntervention | null>(null)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [expandedNodes, setExpandedNodes] = useState<Set<number>>(new Set())

  // Form state
  const [domainName, setDomainName] = useState("")
  const [description, setDescription] = useState("")
  const [selectedParentId, setSelectedParentId] = useState<string>("none")

  useEffect(() => {
    loadDomains()
  }, [])

  const loadDomains = async () => {
    try {
      setLoading(true)
      const [domainsData, treeData] = await Promise.all([
        masterDataService.getDomainInterventions(),
        masterDataService.getDomainInterventionsTree(),
      ])
      setDomains(domainsData)
      setTreeData(treeData)
    } catch (error) {
      console.error("Failed to load domain interventions:", error)
      setError("Failed to load domain interventions")
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    setDomainName("")
    setDescription("")
    setSelectedParentId("none")
    setEditingDomain(null)
    setParentDomain(null)
    setError("")
    setSuccess("")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormLoading(true)
    setError("")

    try {
      const domainData = {
        domainName,
        description: description || undefined,
        parentId: selectedParentId && selectedParentId !== "none" ? parseInt(selectedParentId) : undefined,
      }

      if (editingDomain) {
        await masterDataService.updateDomainIntervention(editingDomain.id, domainData)
        setSuccess("Domain intervention updated successfully")
      } else {
        await masterDataService.createDomainIntervention(domainData)
        setSuccess("Domain intervention created successfully")
      }

      await loadDomains()
      setDialogOpen(false)
      resetForm()
    } catch (error: any) {
      setError(error.response?.data?.message || "Failed to save domain intervention")
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (node: TreeNode) => {
    const domain = domains.find(d => d.id === node.id)
    if (domain) {
      setEditingDomain(domain)
      setDomainName(domain.domainName)
      setDescription(domain.description || "")
      setSelectedParentId(domain.parentId ? domain.parentId.toString() : "none")
      setDialogOpen(true)
    }
  }

  const handleDelete = async (node: TreeNode) => {
    if (confirm("Are you sure you want to delete this domain intervention?")) {
      try {
        await masterDataService.deleteDomainIntervention(node.id)
        setSuccess("Domain intervention deleted successfully")
        await loadDomains()
      } catch (error: any) {
        setError(error.response?.data?.message || "Failed to delete domain intervention")
      }
    }
  }

  const handleAddChild = (parentNode: TreeNode) => {
    const parent = domains.find(d => d.id === parentNode.id)
    if (parent) {
      setParentDomain(parent)
      setSelectedParentId(parent.id.toString())
      setDialogOpen(true)
    }
  }

  const openCreateDialog = () => {
    resetForm()
    setDialogOpen(true)
  }

  // Convert domains to tree nodes
  const convertToTreeNodes = (domains: DomainInterventionHierarchy[]): TreeNode[] => {
    return domains.map(domain => ({
      id: domain.id,
      name: domain.domainName,
      description: domain.description,
      parentId: domain.parentId,
      children: domain.children ? convertToTreeNodes(domain.children) : [],
    }))
  }

  // Filter tree data based on search term
  const filterTreeData = (nodes: DomainInterventionHierarchy[], searchTerm: string): DomainInterventionHierarchy[] => {
    if (!searchTerm) return nodes

    const filtered: DomainInterventionHierarchy[] = []

    const searchInNode = (node: DomainInterventionHierarchy): boolean => {
      const matchesSearch = node.domainName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (node.description && node.description.toLowerCase().includes(searchTerm.toLowerCase()))

      const filteredChildren = node.children ? filterTreeData(node.children, searchTerm) : []
      const hasMatchingChildren = filteredChildren.length > 0

      if (matchesSearch || hasMatchingChildren) {
        filtered.push({
          ...node,
          children: filteredChildren
        })
        return true
      }

      return false
    }

    nodes.forEach(searchInNode)
    return filtered
  }

  const filteredTreeData = filterTreeData(treeData, searchTerm)
  const treeNodes = convertToTreeNodes(filteredTreeData)

  // Calculate statistics
  const getMaxDepth = (nodes: TreeNode[], currentDepth = 0): number => {
    if (!nodes || nodes.length === 0) return currentDepth
    return Math.max(...nodes.map(node =>
      getMaxDepth(node.children || [], currentDepth + 1)
    ))
  }

  const getTotalNodes = (nodes: TreeNode[]): number => {
    return nodes.reduce((total, node) =>
      total + 1 + getTotalNodes(node.children || []), 0
    )
  }

  const maxDepth = getMaxDepth(treeNodes)
  const totalNodes = getTotalNodes(treeNodes)
  const totalFilteredNodes = searchTerm ? getTotalNodes(treeNodes) : domains.length

  // Expand/Collapse all functionality
  const expandAll = () => {
    const getAllNodeIds = (nodes: TreeNode[]): number[] => {
      const ids: number[] = []
      nodes.forEach(node => {
        ids.push(node.id)
        if (node.children) {
          ids.push(...getAllNodeIds(node.children))
        }
      })
      return ids
    }
    setExpandedNodes(new Set(getAllNodeIds(treeNodes)))
  }

  const collapseAll = () => {
    setExpandedNodes(new Set())
  }

  // Filter domains for parent selection (exclude current domain and its descendants)
  const getSelectableParents = (): DomainIntervention[] => {
    if (!editingDomain) return domains // All domains available for new items

    const excludeIds = new Set<number>()

    // Add current domain
    excludeIds.add(editingDomain.id)

    // Add all descendants recursively
    const addDescendants = (parentId: number) => {
      domains.filter(d => d.parentId === parentId).forEach(child => {
        excludeIds.add(child.id)
        addDescendants(child.id)
      })
    }
    addDescendants(editingDomain.id)

    return domains.filter(d => !excludeIds.has(d.id))
  }

  // Create hierarchical display names for parent selection
  const getHierarchicalDisplayName = (domain: DomainIntervention): string => {
    const getPath = (d: DomainIntervention): string[] => {
      if (!d.parentId) return [d.domainName]
      const parent = domains.find(p => p.id === d.parentId)
      if (!parent) return [d.domainName]
      return [...getPath(parent), d.domainName]
    }

    const path = getPath(domain)
    return path.length > 1 ? path.join(' → ') : path[0]
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Domain Interventions</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="mr-2 h-4 w-4" /> Add Domain Intervention
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>
                {editingDomain ? "Edit Domain Intervention" : parentDomain ? `Add Sub-Domain to "${parentDomain.domainName}"` : "Create New Domain Intervention"}
              </DialogTitle>
              <DialogDescription>
                {editingDomain ? "Update domain intervention information." : "Add a new domain intervention to the system."}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
                <div className="space-y-2">
                  <Label htmlFor="domainName">Domain Name *</Label>
                  <Input
                    id="domainName"
                    value={domainName}
                    onChange={(e) => setDomainName(e.target.value)}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="parentId">Parent Domain</Label>
                  <Select value={selectedParentId} onValueChange={setSelectedParentId} disabled={!!parentDomain}>
                    <SelectTrigger>
                      <SelectValue placeholder={parentDomain ? parentDomain.domainName : "Select parent domain (optional)"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Parent (Root Domain)</SelectItem>
                      {getSelectableParents()
                        .sort((a, b) => {
                          // Sort by hierarchy depth first, then by name
                          const aDepth = getHierarchicalDisplayName(a).split(' → ').length
                          const bDepth = getHierarchicalDisplayName(b).split(' → ').length
                          if (aDepth !== bDepth) return aDepth - bDepth
                          return getHierarchicalDisplayName(a).localeCompare(getHierarchicalDisplayName(b))
                        })
                        .map((domain) => (
                          <SelectItem key={domain.id} value={domain.id.toString()}>
                            {getHierarchicalDisplayName(domain)}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={formLoading}>
                  {formLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingDomain ? "Updating..." : "Creating..."}
                    </>
                  ) : editingDomain ? (
                    "Update Domain"
                  ) : (
                    "Create Domain"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {success && (
        <Alert className="bg-green-50 text-green-700 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {error && !dialogOpen && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search domain interventions..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={expandAll}
            className="flex items-center gap-1"
          >
            <ChevronDown className="h-4 w-4" />
            Expand All
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={collapseAll}
            className="flex items-center gap-1"
          >
            <ChevronRight className="h-4 w-4" />
            Collapse All
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              Domain Hierarchy
            </CardTitle>
            <CardDescription>
              Manage domain interventions with unlimited nesting. Create complex hierarchical structures for organizing interventions.
            </CardDescription>
            {!loading && (
              <div className="flex items-center gap-4 text-sm text-muted-foreground mt-2">
                <span>Total: {searchTerm ? totalFilteredNodes : domains.length} domains</span>
                {maxDepth > 0 && <span>Max depth: {maxDepth} levels</span>}
                {searchTerm && <span>Showing: {totalNodes} matches</span>}
              </div>
            )}
          </CardHeader>
          <CardContent>
            <TreeView
              data={treeNodes}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onAddChild={handleAddChild}
              nameKey="name"
              className="border rounded-lg p-4 bg-gray-50"
              defaultExpandAll={false}
              expandedNodes={expandedNodes}
              onExpandedChange={setExpandedNodes}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
