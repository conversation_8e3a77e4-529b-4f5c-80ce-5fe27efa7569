import { NotificationsService } from './notifications.service';
import { CreateNotificationDto } from './dto/create-notification.dto';
import { UpdateNotificationDto } from './dto/update-notification.dto';
export declare class NotificationsController {
    private readonly notificationsService;
    constructor(notificationsService: NotificationsService);
    create(createNotificationDto: CreateNotificationDto): Promise<{
        message: string;
    }>;
    findByUser(userId: string): Promise<any[]>;
    findUnreadByUser(userId: string): Promise<any[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateNotificationDto: UpdateNotificationDto): Promise<{
        message: string;
    }>;
    markAsRead(id: string): Promise<{
        message: string;
    }>;
    markAllAsRead(userId: string): Promise<{
        message: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
