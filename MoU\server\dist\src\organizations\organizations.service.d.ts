import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from './dto';
export declare class OrganizationsService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(currentUserId: string): Promise<({
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    })[]>;
    findOne(id: string, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    create(createOrganizationDto: CreateOrganizationDto, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    update(id: string, updateOrganizationDto: UpdateOrganizationDto, currentUserId: string): Promise<{
        organizationType: {
            id: number;
            typeName: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
        };
        addresses: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            organizationId: string;
            createdAt: Date;
            addressType: import("@prisma/client").$Enums.AddressType;
            country: string;
            province: string | null;
            district: string | null;
            sector: string | null;
            cell: string | null;
            village: string | null;
            street: string;
            avenue: string | null;
            poBox: string;
            postalCode: string | null;
        }[];
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        organizationName: string;
        organizationRegistrationNumber: string;
        organizationPhoneNumber: string;
        organizationEmail: string;
        organizationWebsite: string | null;
        homeCountryRepresentative: string;
        rwandaRepresentative: string;
        organizationRgbNumber: string;
        organizationTypeId: number;
    }>;
    remove(id: string, currentUserId: string): Promise<{
        message: string;
    }>;
    private validateOrganizationData;
    private validateAddresses;
}
