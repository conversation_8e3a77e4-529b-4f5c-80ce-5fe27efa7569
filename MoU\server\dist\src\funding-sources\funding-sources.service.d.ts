import { PrismaService } from '../prisma/prisma.service';
import { CreateFundingSourceDto, UpdateFundingSourceDto } from './dto';
export declare class FundingSourcesService {
    private prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    findAll(): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }[]>;
    findOne(id: number): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    create(createFundingSourceDto: CreateFundingSourceDto, currentUserId: string): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    update(id: number, updateFundingSourceDto: UpdateFundingSourceDto, currentUserId: string): Promise<{
        id: number;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        sourceName: string;
    }>;
    remove(id: number, currentUserId: string): Promise<{
        message: string;
    }>;
}
