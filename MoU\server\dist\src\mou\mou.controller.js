"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MouController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const mou_service_1 = require("./mou.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../auth/guard/jwt-auth.guard");
let MouController = class MouController {
    constructor(mouService) {
        this.mouService = mouService;
    }
    async create(createMouDto, req) {
        return this.mouService.create(createMouDto, req.user.sub);
    }
    async findAll(req) {
        return this.mouService.findAll(req.user.sub);
    }
    async findOne(id, req) {
        return this.mouService.findOne(id, req.user.sub);
    }
    async update(id, updateMouDto, req) {
        return this.mouService.update(id, updateMouDto, req.user.sub);
    }
    async remove(id, req) {
        return this.mouService.remove(id, req.user.sub);
    }
};
exports.MouController = MouController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new MoU (Admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'MoU created successfully', type: dto_1.MouResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'MoU with ID already exists' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Party not found' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateMouDto, Object]),
    __metadata("design:returntype", Promise)
], MouController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all MoUs (Admin sees all, others see their organization\'s MoUs)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns list of MoUs', type: [dto_1.MouResponseDto] }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MouController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get MoU by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'MoU ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns MoU details', type: dto_1.MouResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Access denied' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a MoU (Admin only)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'MoU ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'MoU updated successfully', type: dto_1.MouResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'MoU with ID already exists' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateMouDto, Object]),
    __metadata("design:returntype", Promise)
], MouController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a MoU (Admin only)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'MoU ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'MoU deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden - Admin access required' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'MoU not found' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Cannot delete MoU with active applications' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MouController.prototype, "remove", null);
exports.MouController = MouController = __decorate([
    (0, swagger_1.ApiTags)('mou'),
    (0, common_1.Controller)('mou'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [mou_service_1.MouService])
], MouController);
//# sourceMappingURL=mou.controller.js.map