import { MouApplicationsService } from './mou-applications.service';
import { CreateMouApplicationDto, UpdateMouApplicationDto, UpdateStepDto, AutoSaveDto, CreateResponsibilityDto, UploadDocumentDto } from './dto';
export declare class MouApplicationsController {
    private readonly mouApplicationsService;
    constructor(mouApplicationsService: MouApplicationsService);
    create(createMouApplicationDto: CreateMouApplicationDto, req: any): Promise<{
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    }>;
    createDraft(mouId: string, req: any): Promise<{
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    }>;
    findAll(req: any): Promise<({
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    })[]>;
    findMyApplications(req: any): Promise<({
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    })[]>;
    findOne(id: string, req: any): Promise<{
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: ({
            projectActivities: {
                id: string;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                currency: string | null;
                description: string | null;
                projectId: string;
                activityId: string;
                activityName: string;
                timeline: string | null;
                budgetAllocation: import("@prisma/client/runtime/library").Decimal | null;
            }[];
        } & {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        })[];
        approvalSteps: ({
            reviewer: {
                id: string;
                firstName: string;
                lastName: string;
                email: string;
                role: import("@prisma/client").$Enums.UserRole;
            };
        } & {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        })[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    }>;
    update(id: string, updateMouApplicationDto: UpdateMouApplicationDto, req: any): Promise<{
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    }>;
    updateStep(id: string, stepNumber: number, updateStepDto: UpdateStepDto, req: any): Promise<{
        user: {
            id: string;
            firstName: string;
            lastName: string;
            email: string;
            organization: {
                id: string;
                organizationName: string;
            };
        };
        mou: {
            id: string;
            party: {
                id: string;
                name: string;
            };
            mouId: string;
        };
        documents: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            documentType: string;
            mouApplicationId: string;
            isRequired: boolean;
            documentId: string;
            fileName: string;
            originalName: string;
            mimeType: string;
            fileSize: number;
            filePath: string;
            uploadedAt: Date;
        }[];
        projects: {
            id: string;
            createAt: Date;
            updatedAt: Date;
            deleted: boolean;
            name: string;
            organizationId: string;
            currency: string | null;
            description: string | null;
            projectId: string;
            duration: number;
            startDate: Date | null;
            endDate: Date | null;
            totalBudget: import("@prisma/client/runtime/library").Decimal | null;
            budgetTypeId: number;
            fundingUnitId: number;
            fundingSourceId: number;
            projectDocumentId: string;
            mouApplicationId: string;
        }[];
        approvalSteps: {
            id: string;
            updatedAt: Date;
            role: import("@prisma/client").$Enums.UserRole;
            createdAt: Date;
            projectId: string | null;
            mouApplicationId: string;
            status: import("@prisma/client").$Enums.ApprovalStatusType;
            reviewerId: string;
            comment: string | null;
        }[];
        responsibilities: {
            id: string;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            mouApplicationId: string;
            responsibilityText: string;
            order: number;
        }[];
    } & {
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        responsibility: string | null;
        userId: string | null;
        mouApplicationId: string;
        mouId: string;
        status: import("@prisma/client").$Enums.ApplicationStatus;
        currentStep: number;
        mouDurationYears: number | null;
        signatoryName: string | null;
        signatoryPosition: string | null;
        partyName: string | null;
        completionPercentage: number;
        lastAutoSave: Date | null;
    }>;
    autoSave(id: string, autoSaveDto: AutoSaveDto, req: any): Promise<{
        id: string;
        currentStep: number;
        completionPercentage: number;
        lastAutoSave: Date;
    }>;
    createResponsibility(id: string, createResponsibilityDto: CreateResponsibilityDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        mouApplicationId: string;
        responsibilityText: string;
        order: number;
    }>;
    updateResponsibility(id: string, responsibilityId: string, updateData: Partial<CreateResponsibilityDto>, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        mouApplicationId: string;
        responsibilityText: string;
        order: number;
    }>;
    deleteResponsibility(id: string, responsibilityId: string, req: any): Promise<{
        message: string;
    }>;
    uploadDocument(id: string, file: Express.Multer.File, uploadDocumentDto: UploadDocumentDto, req: any): Promise<{
        id: string;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        documentType: string;
        mouApplicationId: string;
        isRequired: boolean;
        documentId: string;
        fileName: string;
        originalName: string;
        mimeType: string;
        fileSize: number;
        filePath: string;
        uploadedAt: Date;
    }>;
    deleteDocument(id: string, documentId: string, req: any): Promise<{
        message: string;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
