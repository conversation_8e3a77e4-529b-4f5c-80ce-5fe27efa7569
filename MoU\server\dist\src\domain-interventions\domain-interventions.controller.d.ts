import { DomainInterventionsService } from './domain-interventions.service';
import { CreateDomainInterventionDto, UpdateDomainInterventionDto } from './dto';
export declare class DomainInterventionsController {
    private readonly domainInterventionsService;
    constructor(domainInterventionsService: DomainInterventionsService);
    create(createDomainInterventionDto: CreateDomainInterventionDto, req: any): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    findAll(): Promise<({
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: ({
            children: ({
                children: {
                    id: number;
                    updatedAt: Date;
                    deleted: boolean;
                    createdAt: Date;
                    description: string | null;
                    parentId: number | null;
                    domainName: string;
                }[];
            } & {
                id: number;
                updatedAt: Date;
                deleted: boolean;
                createdAt: Date;
                description: string | null;
                parentId: number | null;
                domainName: string;
            })[];
        } & {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        })[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    })[]>;
    findTree(): Promise<any[]>;
    findOne(id: number): Promise<{
        children: any[];
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    update(id: number, updateDomainInterventionDto: UpdateDomainInterventionDto, req: any): Promise<{
        parent: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        };
        children: {
            id: number;
            updatedAt: Date;
            deleted: boolean;
            createdAt: Date;
            description: string | null;
            parentId: number | null;
            domainName: string;
        }[];
    } & {
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
    remove(id: number, req: any): Promise<{
        id: number;
        updatedAt: Date;
        deleted: boolean;
        createdAt: Date;
        description: string | null;
        parentId: number | null;
        domainName: string;
    }>;
}
