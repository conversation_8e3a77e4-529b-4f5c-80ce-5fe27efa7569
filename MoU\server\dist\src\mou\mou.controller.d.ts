import { MouService } from './mou.service';
import { CreateMouDto, UpdateMouDto } from './dto';
export declare class MouController {
    private readonly mouService;
    constructor(mouService: MouService);
    create(createMouDto: CreateMouDto, req: any): Promise<{
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    findAll(req: any): Promise<({
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    })[]>;
    findOne(id: string, req: any): Promise<{
        party: {
            id: string;
            name: string;
            organizationId: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    update(id: string, updateMouDto: UpdateMouDto, req: any): Promise<{
        party: {
            id: string;
            name: string;
            signatory: string;
            position: string;
        };
    } & {
        id: string;
        createAt: Date;
        updatedAt: Date;
        deleted: boolean;
        mouId: string;
        partyId: string;
    }>;
    remove(id: string, req: any): Promise<{
        message: string;
    }>;
}
